import { GoogleGenerativeAI, HarmBlockThreshold, HarmCategory } from '@google/generative-ai';

// Initialize the Google Generative AI with the API key
const initGeminiAI = () => {
  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  
  if (!apiKey) {
    console.error('NEXT_PUBLIC_GEMINI_API_KEY is not defined in environment variables');
    return null;
  }
  
  return new GoogleGenerativeAI(apiKey);
};

// Rate limiting configuration
const RATE_LIMIT = {
  MAX_REQUESTS_PER_MINUTE: 10,  // Adjust based on your API tier
  BATCH_SIZE: 5,                // Process in smaller batches
  DELAY_BETWEEN_BATCHES: 2000,  // 2 seconds between batches
};

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Define financial transaction categories
export const TRANSACTION_CATEGORIES = [
  'Income',
  'Salary',
  'Investments',
  'Groceries',
  'Dining Out',
  'Transportation',
  'Housing',
  'Utilities',
  'Entertainment',
  'Shopping',
  'Health',
  'Education',
  'Travel',
  'Subscriptions',
  'Personal Care',
  'Gifts',
  'Charity',
  'Business',
  'Insurance',
  'Taxes',
  'Savings',
  'Debt Payments',
  'Other'
];

/**
 * Categorize a transaction description using Gemini AI
 * @param description The transaction description to categorize
 * @returns The predicted category or 'Other' if categorization fails
 */
export async function categorizeWithAI(description: string): Promise<string> {
  try {
    const genAI = initGeminiAI();
    
    if (!genAI) {
      console.error('Failed to initialize Gemini AI: API key may be missing or invalid');
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('ai-categorization-error', {
          detail: { 
            message: 'Failed to initialize Gemini AI: API key may be missing or invalid',
            fallbackUsed: true
          }
        }));
      }
      return categorizeWithRules(description);
    }
    
    // Configure the model
    const model = genAI.getGenerativeModel({
      model: 'gemini-1.5-flash',
      safetySettings: [
        {
          category: HarmCategory.HARM_CATEGORY_HARASSMENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
      ],
    });
    
    // Create the prompt for categorization
    const prompt = `
      You are a financial transaction categorizer. 
      Analyze the following transaction description and categorize it into one of these categories:
      ${TRANSACTION_CATEGORIES.join(', ')}
      
      Transaction description: "${description}"
      
      Return ONLY the category name without any additional text or explanation.
    `;
    
    // Generate content with retry mechanism for rate limiting
    let attempts = 0;
    const maxAttempts = 3;
    
    while (attempts < maxAttempts) {
      try {
        console.log(`Attempt ${attempts + 1}/${maxAttempts} to categorize: "${description}"`);
        const result = await model.generateContent(prompt);
        
        // Validate the result object
        if (!result || !result.response) {
          console.warn(`Invalid response from Gemini API for: "${description}"`);
          attempts++;
          continue;
        }
        
        const response = await result.response;
        const text = response.text().trim();
        
        console.log(`Raw API response for "${description}": "${text}"`);
        
        // Validate the response is one of our categories
        if (TRANSACTION_CATEGORIES.includes(text)) {
          console.log(`Direct category match found: "${text}" for "${description}"`);
          return text;
        }
        
        // If the response doesn't match our categories exactly, find the closest match
        const lowerText = text.toLowerCase();
        for (const category of TRANSACTION_CATEGORIES) {
          if (lowerText.includes(category.toLowerCase())) {
            console.log(`Partial category match found: "${category}" for "${description}"`);
            return category;
          }
        }
        
        console.warn(`No category match found for "${description}", response was: "${text}". Using 'Other'`);
        return 'Other';
      } catch (error: any) {
        attempts++;
        console.error(`Error in attempt ${attempts}/${maxAttempts} for "${description}":`, error);
        
        // Check if error is related to rate limiting
        if (error.message && error.message.includes('Resource has been exhausted') && attempts < maxAttempts) {
          console.warn(`Rate limit hit, attempt ${attempts}/${maxAttempts}. Waiting before retry...`);
          await delay(2000 * attempts); // Exponential backoff
          continue;
        }
        
        // If it's not a rate limit error or we've exceeded max attempts, throw it
        throw error;
      }
    }
    
    // If we've exhausted all attempts, fall back to rule-based categorization
    console.warn(`All ${maxAttempts} attempts failed for "${description}", using rule-based categorization`);
    return categorizeWithRules(description);
  } catch (error) {
    console.error('Error categorizing transaction with AI:', error);
    
    // Emit an event for the UI to handle
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('ai-categorization-error', {
        detail: { 
          message: `AI categorization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          fallbackUsed: true
        }
      }));
    }
    
    return categorizeWithRules(description);
  }
}

/**
 * Fallback categorization using rule-based approach
 * @param description The transaction description to categorize
 * @returns The predicted category
 */
export function categorizeWithRules(description: string): string {
  description = description.toLowerCase();
  
  if (description.includes('salary') || description.includes('payroll') || description.includes('deposit')) {
    return 'Income';
  } else if (description.includes('grocery') || description.includes('supermarket') || description.includes('food')) {
    return 'Groceries';
  } else if (description.includes('restaurant') || description.includes('cafe') || description.includes('bar')) {
    return 'Dining Out';
  } else if (description.includes('uber') || description.includes('lyft') || description.includes('taxi')) {
    return 'Transportation';
  } else if (description.includes('netflix') || description.includes('spotify') || description.includes('subscription')) {
    return 'Subscriptions';
  } else if (description.includes('rent') || description.includes('mortgage')) {
    return 'Housing';
  } else if (description.includes('internet') || description.includes('phone') || description.includes('utility')) {
    return 'Utilities';
  }
  
  return 'Other';
}

/**
 * Batch process multiple transactions for categorization
 * Implements rate limiting and batching to avoid API quota issues
 * @param descriptions Array of transaction descriptions to categorize
 * @returns Array of categories in the same order as the input descriptions
 */
export async function batchCategorizeWithAI(descriptions: string[]): Promise<string[]> {
  // If AI categorization is disabled, use rule-based approach for all
  const useAI = process.env.NEXT_PUBLIC_USE_AI_CATEGORIZATION === 'true';
  if (!useAI) {
    console.log('AI categorization is disabled, using rule-based categorization for all transactions');
    return descriptions.map(desc => categorizeWithRules(desc));
  }
  
  // Validate Gemini API key is available
  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  if (!apiKey) {
    console.error('NEXT_PUBLIC_GEMINI_API_KEY is not defined in environment variables');
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('ai-categorization-error', {
        detail: { 
          message: 'Gemini API key is missing. Using rule-based categorization instead.',
          fallbackUsed: true
        }
      }));
    }
    return descriptions.map(desc => categorizeWithRules(desc));
  }
  
  console.log(`Starting batch categorization for ${descriptions.length} transactions`);
  
  const results: string[] = [];
  const { BATCH_SIZE, DELAY_BETWEEN_BATCHES } = RATE_LIMIT;
  let aiSuccessCount = 0;
  let aiFallbackCount = 0;
  
  // Process in smaller batches to avoid rate limiting
  for (let i = 0; i < descriptions.length; i += BATCH_SIZE) {
    const batch = descriptions.slice(i, i + BATCH_SIZE);
    const currentBatch = Math.floor(i / BATCH_SIZE) + 1;
    const totalBatches = Math.ceil(descriptions.length / BATCH_SIZE);
    
    // Calculate progress percentage (0-100)
    const progressPercentage = Math.min(Math.floor((i / descriptions.length) * 100), 90);
    
    // Emit progress event with percentage
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('transaction-processing-update', {
        detail: { 
          message: `Processing batch ${currentBatch}/${totalBatches} (${batch.length} transactions)`,
          progress: progressPercentage,
          currentBatch,
          totalBatches
        }
      }));
    }
    
    console.log(`Processing batch ${currentBatch}/${totalBatches} with ${batch.length} transactions`);
    
    // Process each item in the batch with individual error handling
    const batchPromises = batch.map(async (description, index) => {
      try {
        const category = await categorizeWithAI(description);
        // Check if the category is valid
        if (category && TRANSACTION_CATEGORIES.includes(category)) {
          aiSuccessCount++;
          return category;
        } else {
          // If category is not valid, log and use fallback
          console.warn(`Invalid category returned for "${description}": "${category}". Using rule-based fallback.`);
          aiFallbackCount++;
          return categorizeWithRules(description);
        }
      } catch (error) {
        console.error(`Error categorizing transaction ${i + index}: "${description}"`, error);
        aiFallbackCount++;
        return categorizeWithRules(description); // Fallback to rule-based
      }
    });
    
    try {
      // Wait for the current batch to complete
      const batchResults = await Promise.all(batchPromises);
      
      // Validate batch results
      const validatedResults = batchResults.map((result, idx) => {
        if (!result || typeof result !== 'string' || result.trim() === '') {
          console.warn(`Empty or invalid category result for transaction: "${batch[idx]}". Using 'Other'.`);
          return 'Other';
        }
        return result;
      });
      
      results.push(...validatedResults);
      console.log(`Completed batch ${currentBatch}/${totalBatches}, processed ${validatedResults.length} categories`);
    } catch (batchError) {
      console.error(`Error processing batch ${currentBatch}:`, batchError);
      
      // If batch processing fails, use rule-based categorization for this batch
      const fallbackResults = batch.map(desc => categorizeWithRules(desc));
      results.push(...fallbackResults);
      aiFallbackCount += batch.length;
      
      // Notify user of the issue
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('ai-categorization-error', {
          detail: { 
            message: `Error in batch ${currentBatch}: ${batchError instanceof Error ? batchError.message : 'Unknown error'}. Using fallback categorization.`,
            fallbackUsed: true
          }
        }));
      }
    }
    
    // If there are more batches to process, add a delay to avoid rate limiting
    if (i + BATCH_SIZE < descriptions.length) {
      // Emit progress event about waiting with updated percentage
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('transaction-processing-update', {
          detail: { 
            message: `Waiting ${DELAY_BETWEEN_BATCHES/1000}s to avoid rate limits before next batch...`,
            progress: progressPercentage + 5, // Increment progress slightly during wait
            currentBatch,
            totalBatches
          }
        }));
      }
      
      console.log(`Waiting ${DELAY_BETWEEN_BATCHES/1000}s before processing next batch...`);
      await delay(DELAY_BETWEEN_BATCHES);
    }
  }
  
  // Final progress update
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('transaction-processing-update', {
      detail: { 
        message: `Completed processing all ${descriptions.length} transactions`,
        progress: 100,
        currentBatch: Math.ceil(descriptions.length / BATCH_SIZE),
        totalBatches: Math.ceil(descriptions.length / BATCH_SIZE)
      }
    }));
  }
  
  console.log(`Batch categorization complete. Success: ${aiSuccessCount}, Fallback: ${aiFallbackCount}`);
  
  // Validate final results array
  if (!results || results.length !== descriptions.length) {
    console.error(`Results array length mismatch: expected ${descriptions.length}, got ${results?.length}`);
    // If results are invalid, use rule-based categorization for all
    return descriptions.map(desc => categorizeWithRules(desc));
  }
  
  return results;
}
