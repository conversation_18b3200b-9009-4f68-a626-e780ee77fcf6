import { Transaction, calculateIncomeCategorySummary, calculateExpenseCategorySummary } from '../transaction-utils';

describe('Income and Expense Category Functions', () => {
  // Sample transactions for testing
  const testTransactions: Transaction[] = [
    {
      id: '1',
      date: '2025-05-01',
      description: 'Salary deposit',
      category: 'Salary',
      amount: 3000,
      type: 'income'
    },
    {
      id: '2',
      date: '2025-05-02',
      description: 'Freelance payment',
      category: 'Freelance',
      amount: 500,
      type: 'income'
    },
    {
      id: '3',
      date: '2025-05-03',
      description: 'Dividend payment',
      category: 'Investments',
      amount: 200,
      type: 'income'
    },
    {
      id: '4',
      date: '2025-05-05',
      description: 'Grocery shopping',
      category: 'Groceries',
      amount: 150,
      type: 'expense'
    },
    {
      id: '5',
      date: '2025-05-10',
      description: 'Restaurant dinner',
      category: 'Dining',
      amount: 75,
      type: 'expense'
    },
    {
      id: '6',
      date: '2025-05-15',
      description: 'Monthly rent',
      category: 'Housing',
      amount: 1200,
      type: 'expense'
    }
  ];

  test('calculateIncomeCategorySummary should correctly process income transactions', () => {
    const incomeSummary = calculateIncomeCategorySummary(testTransactions);
    
    // Check that we have the correct number of income categories
    expect(incomeSummary.length).toBe(3);
    
    // Check that the categories are correct
    const categoryNames = incomeSummary.map(c => c.category).sort();
    expect(categoryNames).toEqual(['Freelance', 'Investments', 'Salary'].sort());
    
    // Check the amounts for each category
    const salaryCategory = incomeSummary.find(c => c.category === 'Salary');
    const freelanceCategory = incomeSummary.find(c => c.category === 'Freelance');
    const investmentsCategory = incomeSummary.find(c => c.category === 'Investments');
    
    expect(salaryCategory?.totalAmount).toBe(3000);
    expect(freelanceCategory?.totalAmount).toBe(500);
    expect(investmentsCategory?.totalAmount).toBe(200);
    
    // Check percentages
    const totalIncome = 3000 + 500 + 200; // 3700
    expect(salaryCategory?.percentage).toBeCloseTo((3000 / totalIncome) * 100);
    expect(freelanceCategory?.percentage).toBeCloseTo((500 / totalIncome) * 100);
    expect(investmentsCategory?.percentage).toBeCloseTo((200 / totalIncome) * 100);
    
    // Check counts
    expect(salaryCategory?.count).toBe(1);
    expect(freelanceCategory?.count).toBe(1);
    expect(investmentsCategory?.count).toBe(1);
  });

  test('calculateExpenseCategorySummary should correctly process expense transactions', () => {
    const expenseSummary = calculateExpenseCategorySummary(testTransactions);
    
    // Check that we have the correct number of expense categories
    expect(expenseSummary.length).toBe(3);
    
    // Check that the categories are correct
    const categoryNames = expenseSummary.map(c => c.category).sort();
    expect(categoryNames).toEqual(['Dining', 'Groceries', 'Housing'].sort());
    
    // Check the amounts for each category
    const housingCategory = expenseSummary.find(c => c.category === 'Housing');
    const groceriesCategory = expenseSummary.find(c => c.category === 'Groceries');
    const diningCategory = expenseSummary.find(c => c.category === 'Dining');
    
    expect(housingCategory?.totalAmount).toBe(1200);
    expect(groceriesCategory?.totalAmount).toBe(150);
    expect(diningCategory?.totalAmount).toBe(75);
    
    // Check percentages
    const totalExpenses = 1200 + 150 + 75; // 1425
    expect(housingCategory?.percentage).toBeCloseTo((1200 / totalExpenses) * 100);
    expect(groceriesCategory?.percentage).toBeCloseTo((150 / totalExpenses) * 100);
    expect(diningCategory?.percentage).toBeCloseTo((75 / totalExpenses) * 100);
    
    // Check counts
    expect(housingCategory?.count).toBe(1);
    expect(groceriesCategory?.count).toBe(1);
    expect(diningCategory?.count).toBe(1);
  });

  test('Both functions should handle empty transaction arrays', () => {
    const emptyIncomeSummary = calculateIncomeCategorySummary([]);
    const emptyExpenseSummary = calculateExpenseCategorySummary([]);
    
    expect(emptyIncomeSummary).toEqual([]);
    expect(emptyExpenseSummary).toEqual([]);
  });

  test('Functions should ignore transactions of the wrong type', () => {
    // Income function should only process income transactions
    const onlyExpenses = testTransactions.filter(t => t.type === 'expense');
    const incomeSummary = calculateIncomeCategorySummary(onlyExpenses);
    expect(incomeSummary).toEqual([]);
    
    // Expense function should only process expense transactions
    const onlyIncome = testTransactions.filter(t => t.type === 'income');
    const expenseSummary = calculateExpenseCategorySummary(onlyIncome);
    expect(expenseSummary).toEqual([]);
  });
});
