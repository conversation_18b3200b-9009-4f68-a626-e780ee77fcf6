import * as XLSX from 'xlsx';
import <PERSON> from 'papaparse';
import { categorizeWithAI, categorizeWithRules, batchCategorizeWithAI } from './gemini-ai';

export interface Transaction {
  id: string;
  date: string;
  description: string;
  category: string;
  amount: number;
  type: 'income' | 'expense';
}

export interface CategorySummary {
  category: string;
  totalAmount: number;
  percentage: number;
  count: number;
}

// Transaction row format from CSV/Excel
export interface TransactionRow {
  [key: string]: string | number | undefined;
  Date?: string;
  date?: string;
  TransactionDate?: string;
  Description?: string;
  description?: string;
  Narrative?: string;
  Amount?: string | number;
  amount?: string | number;
  Value?: string | number;
}

export type StatementType = 'csv' | 'xlsx' | 'unknown';

export function detectFileType(file: File): StatementType {
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (extension === 'csv') return 'csv';
  if (extension === 'xlsx' || extension === 'xls') return 'xlsx';
  return 'unknown';
}

export function parseDate(dateString: string): string {
  try {
    // Handle various date formats
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0]; // YYYY-MM-DD
    }
    
    // If standard parsing fails, try custom formats
    // This is a simplified example - real implementation would handle more formats
    const parts = dateString.split(/[\/\-\.]/);
    if (parts.length === 3) {
      // Assume MM/DD/YYYY or DD/MM/YYYY
      const year = parts[2].length === 4 ? parts[2] : `20${parts[2]}`;
      // Default to MM/DD/YYYY format, but this should be configurable
      const month = parts[0].padStart(2, '0');
      const day = parts[1].padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    
    return dateString; // Return as is if parsing fails
  } catch (_) {
    return dateString;
  }
}

export function parseAmount(amountString: string | number): number {
  if (typeof amountString === 'number') return amountString;
  
  // Remove currency symbols and commas
  const cleanedString = amountString.replace(/[$£€,]/g, '');
  
  // Parse the string to a float
  return parseFloat(cleanedString) || 0;
}

// AI-powered categorization function with fallback to rule-based approach
export async function categorizeTransaction(description: string): Promise<string> {
  // Check if AI categorization is enabled
  const useAI = process.env.NEXT_PUBLIC_USE_AI_CATEGORIZATION === 'true';
  
  if (useAI) {
    try {
      // Attempt to categorize with Gemini AI
      const category = await categorizeWithAI(description);
      return category;
    } catch (error) {
      console.error('AI categorization failed, falling back to rules-based approach:', error);
      // Fall back to rule-based categorization if AI fails
      return categorizeWithRules(description);
    }
  } else {
    // Use rule-based categorization if AI is disabled
    return categorizeWithRules(description);
  }
}

export async function processCSV(file: File): Promise<Transaction[]> {
  return new Promise((resolve, reject) => {
    // Emit initial parsing event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('transaction-processing-update', {
        detail: { 
          message: `Parsing CSV file: ${file.name}`,
          progress: 20
        }
      }));
    }
    
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        try {
          // Emit progress event for extraction phase
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('transaction-processing-update', {
              detail: { 
                message: `Extracting data from ${results.data.length} rows`,
                progress: 30
              }
            }));
          }
          
          // Extract all transaction data first
          const transactionData = results.data.map((row: unknown, index: number) => {
            const typedRow = row as TransactionRow;
            // Extract fields - adjust based on expected CSV structure
            const dateField = typedRow.Date || typedRow.date || typedRow.TransactionDate || '';
            const descriptionField = typedRow.Description || typedRow.description || typedRow.Narrative || '';
            const amountField = typedRow.Amount || typedRow.amount || typedRow.Value || 0;
            
            const amount = parseAmount(amountField);
            const type = amount >= 0 ? 'income' : 'expense' as const;
            
            // Emit progress event every 100 rows to show activity
            if (typeof window !== 'undefined' && index % 100 === 0 && index > 0) {
              window.dispatchEvent(new CustomEvent('transaction-processing-update', {
                detail: { 
                  message: `Processed ${index} of ${results.data.length} rows...`,
                  progress: 30 + Math.min(Math.floor((index / results.data.length) * 20), 20)
                }
              }));
            }
            
            return {
              id: `csv-${index}`,
              date: parseDate(dateField as string),
              description: descriptionField as string,
              amount: Math.abs(amount), // Store as positive for consistent math
              type
            };
          });
          
          // Extract descriptions for batch categorization
          const descriptions = transactionData.map(t => t.description);
          
          // Emit progress event
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('transaction-processing-update', {
              detail: { 
                message: `Categorizing ${descriptions.length} transactions in batches...`,
                progress: 50
              }
            }));
          }
          
          // Batch categorize all descriptions at once to avoid rate limiting
          let categories;
          try {
            categories = await batchCategorizeWithAI(descriptions);
            console.log('Categories returned:', categories);
          } catch (error) {
            console.error('Error in batch categorization:', error);
            // Fallback to rule-based categorization if batch categorization fails
            categories = descriptions.map(desc => categorizeWithRules(desc));
          }
          
          // Ensure we have categories for all transactions
          if (!categories || categories.length !== descriptions.length) {
            console.warn('Categories array length mismatch or empty, using fallback categorization');
            categories = descriptions.map(desc => categorizeWithRules(desc));
          }
          
          // Emit progress event for final processing
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('transaction-processing-update', {
              detail: { 
                message: `Finalizing transaction data...`,
                progress: 90
              }
            }));
          }
          
          // Combine the transaction data with the categories
          const transactions = transactionData.map((t, i) => ({
            id: t.id,
            date: t.date,
            description: t.description,
            amount: t.amount,
            category: categories[i] || 'Other', // Ensure a category is always assigned
            type: t.type
          }));
          
          console.log('Final transactions with categories:', transactions.slice(0, 3));
          
          resolve(transactions as Transaction[]);
        } catch (error) {
          console.error('Error processing CSV file:', error);
          reject(error);
        }
      },
      error: (error) => {
        reject(error);
      }
    });
  });
}

export async function processExcel(file: File): Promise<Transaction[]> {
  return new Promise((resolve, reject) => {
    // Emit initial reading event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('transaction-processing-update', {
        detail: { 
          message: `Reading Excel file: ${file.name}`,
          progress: 20
        }
      }));
    }
    
    const reader = new FileReader();
    
    reader.onload = async (e) => {
      try {
        if (!e.target?.result) {
          throw new Error('Failed to read Excel file');
        }
        
        // Emit progress event for parsing phase
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('transaction-processing-update', {
            detail: { 
              message: `Parsing Excel workbook...`,
              progress: 25
            }
          }));
        }
        
        const data = e.target.result as ArrayBuffer;
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Assume first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // Emit progress event for conversion phase
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('transaction-processing-update', {
            detail: { 
              message: `Converting Excel data to JSON...`,
              progress: 30
            }
          }));
        }
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        // Emit progress event for extraction phase
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('transaction-processing-update', {
            detail: { 
              message: `Processing ${jsonData.length} rows from Excel...`,
              progress: 35
            }
          }));
        }
        
        // Extract all transaction data first
        const transactionData = jsonData.map((row: unknown, index: number) => {
          const typedRow = row as TransactionRow;
          // Extract fields - adjust based on expected Excel structure
          const dateField = typedRow.Date || typedRow.date || typedRow.TransactionDate || '';
          const descriptionField = typedRow.Description || typedRow.description || typedRow.Narrative || '';
          const amountField = typedRow.Amount || typedRow.amount || typedRow.Value || 0;
          
          const amount = parseAmount(amountField);
          const type = amount >= 0 ? 'income' : 'expense' as const;
          
          // Emit progress event every 100 rows to show activity
          if (typeof window !== 'undefined' && index % 100 === 0 && index > 0) {
            window.dispatchEvent(new CustomEvent('transaction-processing-update', {
              detail: { 
                message: `Processed ${index} of ${jsonData.length} rows...`,
                progress: 35 + Math.min(Math.floor((index / jsonData.length) * 15), 15)
              }
            }));
          }
          
          return {
            id: `xlsx-${index}`,
            date: parseDate(dateField as string),
            description: descriptionField as string,
            amount: Math.abs(amount), // Store as positive for consistent math
            type
          };
        });
        
        // Extract descriptions for batch categorization
        const descriptions = transactionData.map(t => t.description);
        
        // Emit progress event
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('transaction-processing-update', {
            detail: { 
              message: `Categorizing ${descriptions.length} transactions in batches...`,
              progress: 50
            }
          }));
        }
        
        // Batch categorize all descriptions at once to avoid rate limiting
        let categories;
        try {
          categories = await batchCategorizeWithAI(descriptions);
          console.log('Excel categories returned:', categories);
        } catch (error) {
          console.error('Error in Excel batch categorization:', error);
          // Fallback to rule-based categorization if batch categorization fails
          categories = descriptions.map(desc => categorizeWithRules(desc));
        }
        
        // Ensure we have categories for all transactions
        if (!categories || categories.length !== descriptions.length) {
          console.warn('Excel categories array length mismatch or empty, using fallback categorization');
          categories = descriptions.map(desc => categorizeWithRules(desc));
        }
        
        // Emit progress event for final processing
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('transaction-processing-update', {
            detail: { 
              message: `Finalizing transaction data...`,
              progress: 90
            }
          }));
        }
        
        // Combine the transaction data with the categories
        const transactions = transactionData.map((t, i) => ({
          id: t.id,
          date: t.date,
          description: t.description,
          amount: t.amount,
          category: categories[i] || 'Other', // Ensure a category is always assigned
          type: t.type
        }));
        
        console.log('Final Excel transactions with categories:', transactions.slice(0, 3));
        
        resolve(transactions as Transaction[]);
      } catch (error) {
        console.error('Error processing Excel file:', error);
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    
    reader.readAsArrayBuffer(file);
  });
}

export function calculateExpenseCategorySummary(transactions: Transaction[]): CategorySummary[] {
  const expenseTransactions = transactions.filter(t => t.type === 'expense');
  const totalExpenses = expenseTransactions.reduce((sum, t) => sum + t.amount, 0);
  
  // Group by category
  const categoriesMap = new Map<string, CategorySummary>();
  
  expenseTransactions.forEach(transaction => {
    const { category, amount } = transaction;
    
    if (!categoriesMap.has(category)) {
      categoriesMap.set(category, {
        category,
        totalAmount: 0,
        percentage: 0,
        count: 0
      });
    }
    
    const summary = categoriesMap.get(category)!;
    summary.totalAmount += amount;
    summary.count++;
  });
  
  // Calculate percentages
  const categoryArray = Array.from(categoriesMap.values());
  categoryArray.forEach(summary => {
    summary.percentage = totalExpenses ? (summary.totalAmount / totalExpenses) * 100 : 0;
  });
  
  // Sort by amount descending
  return categoryArray.sort((a, b) => b.totalAmount - a.totalAmount);
}

export function calculateIncomeCategorySummary(transactions: Transaction[]): CategorySummary[] {
  const incomeTransactions = transactions.filter(t => t.type === 'income');
  const totalIncome = incomeTransactions.reduce((sum, t) => sum + t.amount, 0);
  
  // Group by category
  const categoriesMap = new Map<string, CategorySummary>();
  
  incomeTransactions.forEach(transaction => {
    const { category, amount } = transaction;
    
    if (!categoriesMap.has(category)) {
      categoriesMap.set(category, {
        category,
        totalAmount: 0,
        percentage: 0,
        count: 0
      });
    }
    
    const summary = categoriesMap.get(category)!;
    summary.totalAmount += amount;
    summary.count++;
  });
  
  // Calculate percentages
  const categoryArray = Array.from(categoriesMap.values());
  categoryArray.forEach(summary => {
    summary.percentage = totalIncome ? (summary.totalAmount / totalIncome) * 100 : 0;
  });
  
  // Sort by amount descending
  return categoryArray.sort((a, b) => b.totalAmount - a.totalAmount);
}

// Keeping the original function name for backward compatibility
export function calculateCategorySummary(transactions: Transaction[]): CategorySummary[] {
  return calculateExpenseCategorySummary(transactions);
}

export function calculateTotalIncome(transactions: Transaction[]): number {
  return transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);
}

export function calculateTotalExpenses(transactions: Transaction[]): number {
  return transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);
}

export function calculateBalance(transactions: Transaction[]): number {
  return calculateTotalIncome(transactions) - calculateTotalExpenses(transactions);
} 