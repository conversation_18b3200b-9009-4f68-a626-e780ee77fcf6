import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';

export async function middleware(req: NextRequest) {
  // Create response to modify
  const res = NextResponse.next();
  
  // Create Supabase client with the correct setup for middleware
  const supabase = createMiddlewareClient({ req, res });
  
  try {
    // Get session
    const { data } = await supabase.auth.getSession();
    
    // Check auth status for protected routes
    const isProtectedRoute = req.nextUrl.pathname.startsWith('/dashboard');
    const isAuthRoute = req.nextUrl.pathname.startsWith('/login');
    
    console.log(`Middleware: Path=${req.nextUrl.pathname}, Protected=${isProtectedRoute}, AuthRoute=${isAuthRoute}, HasSession=${!!data.session}`);

    // If trying to access dashboard without a session, redirect to login
    if (isProtectedRoute && !data.session) {
      console.log("Middleware: Redirecting to login (no session for protected route)");
      return NextResponse.redirect(new URL('/login', req.url));
    }

    // If already logged in and trying to access login page, redirect to dashboard
    if (isAuthRoute && data.session) {
      console.log("Middleware: Redirecting to dashboard (session exists on auth route)");
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }
    
    // For all other cases, continue with the request
    return res;
  } catch (error) {
    console.error("Middleware error:", error);
    
    // On error, allow the request to continue
    // but redirect to login for protected routes
    if (req.nextUrl.pathname.startsWith('/dashboard')) {
      return NextResponse.redirect(new URL('/login', req.url));
    }
    
    return res;
  }
}

export const config = {
  matcher: ['/dashboard/:path*', '/login'],
}; 