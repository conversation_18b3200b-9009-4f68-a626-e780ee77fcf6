'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowUpCircle, ArrowDownCircle, Wallet, Upload, FileSpreadsheet } from 'lucide-react';
import { CashflowChart } from '@/components/cashflow-chart';
import { 
  Transaction,
  calculateTotalIncome,
  calculateTotalExpenses,
  calculateBalance
} from '@/lib/transaction-utils';

interface DashboardOverviewProps {
  transactions: Transaction[];
  onUploadClick: () => void;
}

export function DashboardOverview({ transactions, onUploadClick }: DashboardOverviewProps) {
  const totalIncome = calculateTotalIncome(transactions);
  const totalExpenses = calculateTotalExpenses(transactions);
  const balance = calculateBalance(transactions);
  
  // If there are no transactions, show a CTA to upload bank statements
  if (!transactions || transactions.length === 0) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
            <CardHeader className="pb-3">
              <div className="flex items-center">
                <ArrowUpCircle className="h-5 w-5 text-[#10b981] mr-2" />
                <div>
                  <CardTitle className="text-lg font-medium text-gray-800">Total Income</CardTitle>
                  <CardDescription className="text-gray-500">No data yet</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-800">$0.00</div>
            </CardContent>
          </Card>
          
          <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
            <CardHeader className="pb-3">
              <div className="flex items-center">
                <ArrowDownCircle className="h-5 w-5 text-[#ef4444] mr-2" />
                <div>
                  <CardTitle className="text-lg font-medium text-gray-800">Total Expenses</CardTitle>
                  <CardDescription className="text-gray-500">No data yet</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-800">$0.00</div>
            </CardContent>
          </Card>
          
          <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
            <CardHeader className="pb-3">
              <div className="flex items-center">
                <Wallet className="h-5 w-5 text-[#3b82f6] mr-2" />
                <div>
                  <CardTitle className="text-lg font-medium text-gray-800">Balance</CardTitle>
                  <CardDescription className="text-gray-500">No data yet</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-800">$0.00</div>
            </CardContent>
          </Card>
        </div>
        
        <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
          <CardContent className="flex flex-col items-center justify-center py-12 px-4">
            <div className="text-center space-y-4 max-w-md">
              <div className="bg-gray-100 rounded-full p-4 w-16 h-16 flex items-center justify-center mx-auto">
                <Upload className="h-8 w-8 text-gray-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800">No transactions yet</h3>
              <p className="text-gray-600">
                Upload your bank statements in PDF or Excel format to get started with tracking your finances.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
                <Button 
                  onClick={onUploadClick}
                  className="bg-[#10b981] hover:bg-[#0d9668] text-white flex items-center gap-2"
                >
                  <FileSpreadsheet className="h-4 w-4" />
                  Upload Bank Statement
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
          <CardHeader className="pb-3">
            <div className="flex items-center">
              <ArrowUpCircle className="h-5 w-5 text-[#10b981] mr-2" />
              <div>
                <CardTitle className="text-lg font-medium text-gray-800">Total Income</CardTitle>
                <CardDescription className="text-gray-500">All time</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-800">${totalIncome.toFixed(2)}</div>
          </CardContent>
        </Card>
        
        <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
          <CardHeader className="pb-3">
            <div className="flex items-center">
              <ArrowDownCircle className="h-5 w-5 text-[#ef4444] mr-2" />
              <div>
                <CardTitle className="text-lg font-medium text-gray-800">Total Expenses</CardTitle>
                <CardDescription className="text-gray-500">All time</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-800">${totalExpenses.toFixed(2)}</div>
          </CardContent>
        </Card>
        
        <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
          <CardHeader className="pb-3">
            <div className="flex items-center">
              <Wallet className="h-5 w-5 text-[#3b82f6] mr-2" />
              <div>
                <CardTitle className="text-lg font-medium text-gray-800">Balance</CardTitle>
                <CardDescription className="text-gray-500">Income - Expenses</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${balance >= 0 ? 'text-[#10b981]' : 'text-[#ef4444]'}`}>
              ${balance.toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>
      
      <CashflowChart transactions={transactions} />
    </div>
  );
}
