'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3 } from 'lucide-react';
import { 
  ResponsiveContainer, 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend 
} from 'recharts';
import { Transaction } from '@/lib/transaction-utils';

interface CashflowChartProps {
  transactions: Transaction[];
}

export function CashflowChart({ transactions }: CashflowChartProps) {
  const [timeframe, setTimeframe] = useState<'week' | 'month' | 'year'>('month');
  
  // Group transactions by date and calculate daily cashflow
  const processTransactions = () => {
    if (!transactions || transactions.length === 0) {
      return [];
    }

    // Create a map to store daily totals
    const dailyTotals = new Map<string, { date: string; income: number; expenses: number; balance: number }>();
    
    // Get date range based on timeframe
    const today = new Date();
    let startDate: Date;
    
    switch (timeframe) {
      case 'week':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 7);
        break;
      case 'year':
        startDate = new Date(today);
        startDate.setFullYear(today.getFullYear() - 1);
        break;
      case 'month':
      default:
        startDate = new Date(today);
        startDate.setMonth(today.getMonth() - 1);
    }
    
    // Initialize dates in the range
    let currentDate = new Date(startDate);
    while (currentDate <= today) {
      const dateKey = currentDate.toISOString().split('T')[0];
      dailyTotals.set(dateKey, {
        date: dateKey,
        income: 0,
        expenses: 0,
        balance: 0
      });
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // Add transaction data
    transactions.forEach(transaction => {
      const transactionDate = transaction.date.split('T')[0];
      if (!dailyTotals.has(transactionDate)) {
        return; // Skip transactions outside our date range
      }
      
      const dailyData = dailyTotals.get(transactionDate)!;
      
      if (transaction.type === 'income') {
        dailyData.income += transaction.amount;
      } else {
        dailyData.expenses += transaction.amount;
      }
      
      dailyData.balance = dailyData.income - dailyData.expenses;
    });
    
    // Convert map to array and sort by date
    const result = Array.from(dailyTotals.values());
    result.sort((a, b) => a.date.localeCompare(b.date));
    
    // Calculate running balance
    let runningBalance = 0;
    result.forEach(day => {
      runningBalance += (day.income - day.expenses);
      day.balance = runningBalance;
    });
    
    return result;
  };
  
  const chartData = processTransactions();
  
  // Handle empty state
  if (chartData.length === 0) {
    return (
      <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
        <CardHeader className="pb-3">
          <div className="flex items-center">
            <BarChart3 className="h-5 w-5 text-[#10b981] mr-2" />
            <div>
              <CardTitle className="text-lg font-medium text-gray-800">Cashflow Chart</CardTitle>
              <CardDescription className="text-gray-500">No transaction data available</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-64">
          <p className="text-gray-500">Upload transactions to see your cashflow chart</p>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className="border border-gray-200 shadow-lg hover:shadow-xl transition-shadow bg-white">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BarChart3 className="h-5 w-5 text-[#10b981] mr-2" />
            <div>
              <CardTitle className="text-lg font-medium text-gray-800">Cashflow Chart</CardTitle>
              <CardDescription className="text-gray-500">Balance over time</CardDescription>
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setTimeframe('week')}
              className={`px-2 py-1 text-xs rounded ${timeframe === 'week' ? 'bg-[#10b981] text-white' : 'bg-gray-100 text-gray-700'}`}
            >
              Week
            </button>
            <button
              onClick={() => setTimeframe('month')}
              className={`px-2 py-1 text-xs rounded ${timeframe === 'month' ? 'bg-[#10b981] text-white' : 'bg-gray-100 text-gray-700'}`}
            >
              Month
            </button>
            <button
              onClick={() => setTimeframe('year')}
              className={`px-2 py-1 text-xs rounded ${timeframe === 'year' ? 'bg-[#10b981] text-white' : 'bg-gray-100 text-gray-700'}`}
            >
              Year
            </button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-64 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
              <defs>
                <linearGradient id="colorIncome" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0} />
                </linearGradient>
                <linearGradient id="colorExpenses" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                </linearGradient>
                <linearGradient id="colorBalance" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
              <XAxis 
                dataKey="date" 
                tick={{ fontSize: 10 }} 
                tickFormatter={(date) => {
                  const d = new Date(date);
                  return timeframe === 'year' 
                    ? `${d.getMonth() + 1}/${d.getFullYear().toString().substr(2)}`
                    : `${d.getMonth() + 1}/${d.getDate()}`;
                }}
              />
              <YAxis tick={{ fontSize: 10 }} />
              <Tooltip 
                formatter={(value: number) => [`$${value.toFixed(2)}`, '']}
                labelFormatter={(label) => {
                  const date = new Date(label);
                  return date.toLocaleDateString();
                }}
              />
              <Legend />
              <Area 
                type="monotone" 
                dataKey="balance" 
                stroke="#3b82f6" 
                fillOpacity={1} 
                fill="url(#colorBalance)" 
                name="Balance"
              />
              <Area 
                type="monotone" 
                dataKey="income" 
                stroke="#10b981" 
                fillOpacity={1} 
                fill="url(#colorIncome)" 
                name="Income"
              />
              <Area 
                type="monotone" 
                dataKey="expenses" 
                stroke="#ef4444" 
                fillOpacity={1} 
                fill="url(#colorExpenses)" 
                name="Expenses"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
