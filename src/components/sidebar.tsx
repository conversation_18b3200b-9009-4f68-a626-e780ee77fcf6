'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { supabase } from '@/lib/supabase';
import {
  LayoutGrid,
  TrendingUp,
  FileText,
  Bell,
  Columns3,
  Clock,
  Book,
  LogOut,
  Search,
  X,
  PanelLeft,
  BarChart3
} from 'lucide-react';

interface SidebarProps {
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function Sidebar({ className = '', isCollapsed = false, onToggleCollapse }: SidebarProps) {
  const [user, setUser] = useState<{
    email?: string;
    user_metadata?: {
      full_name?: string;
      avatar_url?: string;
    };
  } | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const getUser = async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session?.user) {
        setUser(data.session.user);
      }
    };
    getUser();
  }, []);

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      router.replace('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const userInitials = user?.user_metadata?.full_name 
    ? user.user_metadata.full_name.split(' ').map((n: string) => n[0]).join('').toUpperCase()
    : user?.email?.substring(0, 2).toUpperCase() || 'U';

  const isActive = (path: string) => pathname === path;

  // Collapsed sidebar version
  if (isCollapsed) {
    return (
      <div className={`w-16 h-screen bg-white/80 backdrop-blur-md shadow-2xl flex flex-col overflow-hidden border border-gray-200 ${className}`}>
        {/* Collapsed Header */}
        <div className="flex items-center justify-center px-2 py-7 border-b border-gray-200">
          <div className="w-8 h-8 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700 rounded-[16px] shadow">
            <span className="text-white text-sm font-bold drop-shadow-sm select-none">{userInitials}</span>
          </div>
        </div>

        {/* Collapsed Menu */}
        <nav className="flex-1 px-2 pt-4 pb-2 overflow-y-auto flex flex-col space-y-2">
          <Link
            href="/dashboard"
            className={`flex items-center justify-center p-3 rounded-2xl transition group ${
              isActive('/dashboard')
                ? 'bg-gray-100 text-gray-900'
                : 'hover:bg-gray-100 text-gray-800'
            }`}
            title="Analytics & Insights"
          >
            <BarChart3 className={`w-5 h-5 transition ${
              isActive('/dashboard')
                ? 'text-gray-700'
                : 'text-gray-400 group-hover:text-blue-600'
            }`} />
          </Link>

          <Link
            href="/dashboard/transactions"
            className={`flex items-center justify-center p-3 rounded-2xl transition group ${
              isActive('/dashboard/transactions')
                ? 'bg-gray-100 text-gray-900'
                : 'hover:bg-gray-100 text-gray-800'
            }`}
            title="Transaction History"
          >
            <FileText className={`w-5 h-5 transition ${
              isActive('/dashboard/transactions')
                ? 'text-gray-700'
                : 'text-gray-400 group-hover:text-blue-600'
            }`} />
          </Link>

          <Link 
            href="/dashboard/notifications" 
            className={`flex items-center justify-center p-3 rounded-2xl relative transition group ${
              isActive('/dashboard/notifications')
                ? 'bg-gray-100 text-gray-900'
                : 'hover:bg-gray-100 text-gray-800'
            }`}
            title="Notifications"
          >
            <Bell className={`w-5 h-5 transition ${
              isActive('/dashboard/notifications') 
                ? 'text-gray-700' 
                : 'text-gray-400 group-hover:text-blue-600'
            }`} />
            <span className="absolute -top-1 -right-1 inline-flex items-center justify-center text-xs font-semibold bg-blue-600 text-white rounded-full w-4 h-4">7</span>
          </Link>

          <Link 
            href="/dashboard/statements" 
            className={`flex items-center justify-center p-3 rounded-2xl transition group ${
              isActive('/dashboard/statements')
                ? 'bg-gray-100 text-gray-900'
                : 'hover:bg-gray-100 text-gray-800'
            }`}
            title="Bank Statements"
          >
            <Columns3 className={`w-5 h-5 transition ${
              isActive('/dashboard/statements') 
                ? 'text-gray-700' 
                : 'text-gray-400 group-hover:text-blue-600'
            }`} />
          </Link>

          <Link 
            href="/dashboard/reports" 
            className={`flex items-center justify-center p-3 rounded-2xl transition group ${
              isActive('/dashboard/reports')
                ? 'bg-gray-100 text-gray-900'
                : 'hover:bg-gray-100 text-gray-800'
            }`}
            title="Reports"
          >
            <Clock className={`w-5 h-5 transition ${
              isActive('/dashboard/reports') 
                ? 'text-gray-700' 
                : 'text-gray-400 group-hover:text-blue-600'
            }`} />
          </Link>

          <Link 
            href="/dashboard/export" 
            className={`flex items-center justify-center p-3 rounded-2xl transition group ${
              isActive('/dashboard/export')
                ? 'bg-gray-100 text-gray-900'
                : 'hover:bg-gray-100 text-gray-800'
            }`}
            title="Export Data"
          >
            <Book className={`w-5 h-5 transition ${
              isActive('/dashboard/export') 
                ? 'text-gray-700' 
                : 'text-gray-400 group-hover:text-blue-600'
            }`} />
          </Link>
        </nav>

        {/* Collapsed Footer */}
        <div className="px-2 py-4 border-t border-gray-200 space-y-2">
          {onToggleCollapse && (
            <button 
              onClick={onToggleCollapse}
              className="flex items-center justify-center p-3 rounded-2xl hover:bg-gray-100 transition group w-full" 
              title="Expand sidebar"
            >
              <PanelLeft className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition" />
            </button>
          )}
          <button 
            onClick={handleSignOut}
            className="flex items-center justify-center p-3 rounded-2xl hover:bg-red-50 transition group w-full" 
            title="Log out"
          >
            <LogOut className="w-5 h-5 text-red-500" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-[340px] h-screen bg-white/80 backdrop-blur-md shadow-2xl flex flex-col overflow-hidden border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-3 px-6 py-7 border-b border-gray-200">
        <div className="w-10 h-10 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700 rounded-[24px] shadow">
          <span className="text-white text-2xl font-bold drop-shadow-sm select-none">{userInitials}</span>
        </div>
        <span className="ml-1 text-xl font-semibold tracking-tight text-gray-900 select-none flex-1">Cashflowee</span>
        {onToggleCollapse && (
          <button 
            onClick={onToggleCollapse}
            className="rounded-full flex items-center justify-center w-9 h-9 hover:bg-gray-100 transition group" 
            aria-label="Close sidebar"
          >
            <X className="w-6 h-6 text-gray-400 group-hover:text-gray-700 transition" />
          </button>
        )}
      </div>

      {/* Search */}
      <div className="px-6 pt-6 pb-2">
        <div className="relative">
          <input 
            type="text" 
            placeholder="Search" 
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-3 py-2.5 bg-gray-100 border border-gray-200 rounded-xl text-[15px] text-gray-900 placeholder-gray-400 focus:outline-none focus:border-gray-300 focus:bg-white transition" 
          />
          <Search className="absolute left-3 top-[13px] w-4 h-4 text-gray-400" />
        </div>
      </div>

      {/* Menu */}
      <nav className="flex-1 px-2 pt-2 pb-2 overflow-y-auto flex flex-col">
        {/* Home Section */}
        <div className="mb-2">
          <div className="uppercase text-xs text-gray-400 font-semibold mb-1 px-4">Home</div>
          <Link
            href="/dashboard"
            className={`flex items-center gap-3 px-4 py-2.5 rounded-2xl transition group ${
              isActive('/dashboard')
                ? 'bg-gray-100 text-gray-900 font-semibold'
                : 'hover:bg-gray-100 text-gray-800 font-medium'
            }`}
          >
            <BarChart3 className={`w-5 h-5 transition ${
              isActive('/dashboard')
                ? 'text-gray-700'
                : 'text-gray-400 group-hover:text-blue-600'
            }`} />
            Analytics & Insights
          </Link>
        </div>

        {/* Applications Section */}
        <div className="mb-2">
          <div className="uppercase text-xs text-gray-400 font-semibold mb-1 px-4">Applications</div>
          <ul className="space-y-[2px]">
            <li>
              <Link 
                href="/dashboard/transactions" 
                className={`flex items-center gap-3 px-4 py-2.5 rounded-2xl transition group ${
                  isActive('/dashboard/transactions')
                    ? 'bg-gray-100 text-gray-900 font-semibold'
                    : 'hover:bg-gray-100 text-gray-800 font-medium'
                }`}
              >
                <FileText className={`w-5 h-5 transition ${
                  isActive('/dashboard/transactions') 
                    ? 'text-gray-700' 
                    : 'text-gray-400 group-hover:text-blue-600'
                }`} />
                Transaction History
              </Link>
            </li>

            <li>
              <Link 
                href="/dashboard/notifications" 
                className={`flex items-center gap-3 px-4 py-2.5 rounded-2xl hover:bg-gray-100 relative transition text-gray-800 font-medium group ${
                  isActive('/dashboard/notifications') ? 'bg-gray-100 text-gray-900 font-semibold' : ''
                }`}
              >
                <Bell className={`w-5 h-5 transition ${
                  isActive('/dashboard/notifications') 
                    ? 'text-gray-700' 
                    : 'text-gray-400 group-hover:text-blue-600'
                }`} />
                Notifications
                <span className="ml-auto relative flex items-center">
                  <span className="inline-flex items-center justify-center text-xs font-semibold bg-blue-600 text-white rounded-full w-5 h-5">7</span>
                </span>
              </Link>
            </li>
          </ul>
        </div>

        {/* Tools Section */}
        <div>
          <div className="uppercase text-xs text-gray-400 font-semibold mb-1 px-4">Tools</div>
          <ul className="space-y-[2px]">
            <li>
              <Link 
                href="/dashboard/statements" 
                className={`flex items-center gap-3 px-4 py-2.5 rounded-2xl hover:bg-gray-100 transition text-gray-800 font-medium group ${
                  isActive('/dashboard/statements') ? 'bg-gray-100 text-gray-900 font-semibold' : ''
                }`}
              >
                <Columns3 className={`w-5 h-5 transition ${
                  isActive('/dashboard/statements') 
                    ? 'text-gray-700' 
                    : 'text-gray-400 group-hover:text-blue-600'
                }`} />
                Bank Statements
              </Link>
            </li>
            <li>
              <Link 
                href="/dashboard/reports" 
                className={`flex items-center gap-3 px-4 py-2.5 rounded-2xl hover:bg-gray-100 transition text-gray-800 font-medium group ${
                  isActive('/dashboard/reports') ? 'bg-gray-100 text-gray-900 font-semibold' : ''
                }`}
              >
                <Clock className={`w-5 h-5 transition ${
                  isActive('/dashboard/reports') 
                    ? 'text-gray-700' 
                    : 'text-gray-400 group-hover:text-blue-600'
                }`} />
                Reports
              </Link>
            </li>
            <li>
              <Link 
                href="/dashboard/export" 
                className={`flex items-center gap-3 px-4 py-2.5 rounded-2xl hover:bg-gray-100 transition text-gray-800 font-medium group ${
                  isActive('/dashboard/export') ? 'bg-gray-100 text-gray-900 font-semibold' : ''
                }`}
              >
                <Book className={`w-5 h-5 transition ${
                  isActive('/dashboard/export') 
                    ? 'text-gray-700' 
                    : 'text-gray-400 group-hover:text-blue-600'
                }`} />
                Export Data
              </Link>
            </li>
          </ul>
        </div>
      </nav>

      {/* Footer/Profile */}
      <div className="flex items-center gap-3 px-6 py-5 border-t border-gray-200 bg-gradient-to-b from-white/60 to-white/90">
        <div className="w-10 h-10 rounded-2xl object-cover border border-gray-200 shadow-sm bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700 flex items-center justify-center">
          {user?.user_metadata?.avatar_url ? (
            <img 
              src={user.user_metadata.avatar_url} 
              alt="User" 
              className="w-10 h-10 rounded-2xl object-cover"
            />
          ) : (
            <span className="text-white text-sm font-bold">{userInitials}</span>
          )}
        </div>
        <div className="flex-1">
          <div className="font-semibold text-gray-900 text-[15px] leading-tight">
            {user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User'}
          </div>
        </div>
        <button 
          onClick={handleSignOut}
          className="ml-auto flex items-center gap-2 px-3 py-2 rounded-xl border border-red-200 bg-gradient-to-r from-red-50 to-white hover:from-red-100 hover:to-red-100 transition shadow-sm" 
          aria-label="Log out"
        >
          <LogOut className="w-5 h-5 text-red-500" />
          <span className="text-red-600 text-sm font-semibold">Log out</span>
        </button>
      </div>
    </div>
  );
} 