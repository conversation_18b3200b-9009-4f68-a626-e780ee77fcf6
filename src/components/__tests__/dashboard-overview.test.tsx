import React from 'react';
import { render, screen } from '@testing-library/react';
import { DashboardOverview } from '../dashboard-overview';
import '@testing-library/jest-dom';

// Mock the CashflowChart component
jest.mock('../cashflow-chart', () => ({
  CashflowChart: () => <div data-testid="mock-cashflow-chart">Mocked Cashflow Chart</div>
}));

describe('DashboardOverview Component', () => {
  const mockTransactions = [
    { 
      id: '1', 
      date: '2025-05-01', 
      description: 'Salary', 
      category: 'Income', 
      amount: 3000, 
      type: 'income' 
    },
    { 
      id: '2', 
      date: '2025-05-05', 
      description: 'Rent', 
      category: 'Housing', 
      amount: 1200, 
      type: 'expense' 
    },
    { 
      id: '3', 
      date: '2025-05-10', 
      description: 'Groceries', 
      category: 'Food', 
      amount: 150, 
      type: 'expense' 
    }
  ];

  const mockOnUploadClick = jest.fn();

  test('renders empty state with CTA when no transactions', () => {
    render(<DashboardOverview transactions={[]} onUploadClick={mockOnUploadClick} />);
    
    // Check for empty state message
    expect(screen.getByText('No transactions yet')).toBeInTheDocument();
    
    // Check for CTA button
    const uploadButton = screen.getByText('Upload Bank Statement');
    expect(uploadButton).toBeInTheDocument();
    
    // Check for zero values in metrics
    expect(screen.getAllByText('$0.00')).toHaveLength(3);
  });

  test('renders metrics and chart when transactions exist', () => {
    render(<DashboardOverview transactions={mockTransactions} onUploadClick={mockOnUploadClick} />);
    
    // Check for metrics
    expect(screen.getByText('$3000.00')).toBeInTheDocument(); // Total Income
    expect(screen.getByText('$1350.00')).toBeInTheDocument(); // Total Expenses
    expect(screen.getByText('$1650.00')).toBeInTheDocument(); // Balance
    
    // Check for chart
    expect(screen.getByTestId('mock-cashflow-chart')).toBeInTheDocument();
  });
});
