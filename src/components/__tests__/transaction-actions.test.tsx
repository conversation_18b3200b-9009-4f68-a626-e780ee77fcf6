import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TransactionActions } from '../transaction-actions';
import { Transaction } from '@/lib/transaction-utils';

// Mock date-fns functions
jest.mock('date-fns', () => ({
  format: jest.fn(() => '2025-05-22'),
  parse: jest.fn(() => new Date('2025-05-22'))
}));

describe('TransactionActions', () => {
  const mockTransaction: Transaction = {
    id: 'test-id',
    date: '2025-05-22',
    description: 'Test transaction',
    category: 'Food & Dining',
    amount: 50,
    type: 'expense'
  };
  
  const mockUpdateTransaction = jest.fn();
  const mockDeleteTransaction = jest.fn();
  
  beforeEach(() => {
    mockUpdateTransaction.mockClear();
    mockDeleteTransaction.mockClear();
  });
  
  it('renders the action buttons correctly', () => {
    render(
      <TransactionActions 
        transaction={mockTransaction}
        onUpdateTransaction={mockUpdateTransaction}
        onDeleteTransaction={mockDeleteTransaction}
      />
    );
    
    // Check if the edit and delete buttons are rendered
    expect(screen.getByRole('button', { name: '' })).toBeInTheDocument(); // Edit button
    expect(screen.getAllByRole('button', { name: '' })[1]).toBeInTheDocument(); // Delete button
  });
  
  it('opens the edit dialog when edit button is clicked', async () => {
    render(
      <TransactionActions 
        transaction={mockTransaction}
        onUpdateTransaction={mockUpdateTransaction}
        onDeleteTransaction={mockDeleteTransaction}
      />
    );
    
    // Click the edit button
    await userEvent.click(screen.getByRole('button', { name: '' }));
    
    // Check if the edit dialog is opened
    expect(screen.getByText('Edit Transaction')).toBeInTheDocument();
    expect(screen.getByText('Update the details of your transaction.')).toBeInTheDocument();
    
    // Check if the form is pre-filled with transaction data
    expect(screen.getByDisplayValue('50')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test transaction')).toBeInTheDocument();
  });
  
  it('updates a transaction with valid data', async () => {
    render(
      <TransactionActions 
        transaction={mockTransaction}
        onUpdateTransaction={mockUpdateTransaction}
        onDeleteTransaction={mockDeleteTransaction}
      />
    );
    
    // Open the edit dialog
    await userEvent.click(screen.getByRole('button', { name: '' }));
    
    // Update the form fields
    await userEvent.clear(screen.getByDisplayValue('50'));
    await userEvent.type(screen.getByDisplayValue(''), '75');
    
    await userEvent.clear(screen.getByDisplayValue('Test transaction'));
    await userEvent.type(screen.getByDisplayValue(''), 'Updated transaction');
    
    // Submit the form
    await userEvent.click(screen.getByRole('button', { name: 'Update Transaction' }));
    
    // Check if the onUpdateTransaction callback was called with the correct data
    await waitFor(() => {
      expect(mockUpdateTransaction).toHaveBeenCalledWith('test-id', {
        date: '2025-05-22',
        description: 'Updated transaction',
        category: 'Food & Dining',
        amount: 75,
        type: 'expense'
      });
    });
  });
  
  it('opens the delete confirmation dialog when delete button is clicked', async () => {
    render(
      <TransactionActions 
        transaction={mockTransaction}
        onUpdateTransaction={mockUpdateTransaction}
        onDeleteTransaction={mockDeleteTransaction}
      />
    );
    
    // Click the delete button
    await userEvent.click(screen.getAllByRole('button', { name: '' })[1]);
    
    // Check if the delete confirmation dialog is opened
    expect(screen.getByText('Are you sure?')).toBeInTheDocument();
    expect(screen.getByText('This will permanently delete this transaction. This action cannot be undone.')).toBeInTheDocument();
  });
  
  it('deletes a transaction when confirmed', async () => {
    render(
      <TransactionActions 
        transaction={mockTransaction}
        onUpdateTransaction={mockUpdateTransaction}
        onDeleteTransaction={mockDeleteTransaction}
      />
    );
    
    // Open the delete confirmation dialog
    await userEvent.click(screen.getAllByRole('button', { name: '' })[1]);
    
    // Click the delete button
    await userEvent.click(screen.getByRole('button', { name: 'Delete' }));
    
    // Check if the onDeleteTransaction callback was called with the correct id
    await waitFor(() => {
      expect(mockDeleteTransaction).toHaveBeenCalledWith('test-id');
    });
  });
});
