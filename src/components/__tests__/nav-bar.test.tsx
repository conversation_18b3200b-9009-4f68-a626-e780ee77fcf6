import React from 'react';
import { render, screen } from '@testing-library/react';
import { NavBar } from '../ui/nav-bar';

// Mock the framer-motion to avoid issues with jest
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

describe('NavBar Component', () => {
  const mockItems = [
    { name: "Home", url: "#home", icon: "home" },
    { name: "Features", url: "#features", icon: "features" },
  ];

  it('renders navigation items correctly', () => {
    render(<NavBar items={mockItems} />);
    
    // Check if navigation items are rendered
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Features')).toBeInTheDocument();
  });

  it('has pointer-events-none on container and pointer-events-auto on clickable elements', () => {
    const { container } = render(<NavBar items={mockItems} />);
    
    // Get the main container div
    const mainContainer = container.firstChild as HTMLElement;
    
    // Check if the main container has pointer-events-none
    expect(mainContainer.className).toContain('pointer-events-none');
    
    // Check if the navigation bar has pointer-events-auto
    const navBar = mainContainer.firstChild as HTMLElement;
    expect(navBar.className).toContain('pointer-events-auto');
    
    // Check if the links have pointer-events-auto
    const links = container.querySelectorAll('a');
    links.forEach(link => {
      expect(link.className).toContain('pointer-events-auto');
    });
  });
});
