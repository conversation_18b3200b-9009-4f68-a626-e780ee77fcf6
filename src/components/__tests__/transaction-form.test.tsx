import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TransactionForm } from '../transaction-form';
import { Transaction } from '@/lib/transaction-utils';

// Mock the uuid module
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid')
}));

// Mock date-fns format function
jest.mock('date-fns', () => ({
  format: jest.fn(() => '2025-05-22')
}));

describe('TransactionForm', () => {
  const mockOnAddTransaction = jest.fn();
  
  beforeEach(() => {
    mockOnAddTransaction.mockClear();
  });
  
  it('renders the form correctly', () => {
    render(<TransactionForm onAddTransaction={mockOnAddTransaction} />);
    
    // Check if the button to open the form is rendered
    expect(screen.getByText('Add Transaction')).toBeInTheDocument();
  });
  
  it('opens the dialog when the button is clicked', async () => {
    render(<TransactionForm onAddTransaction={mockOnAddTransaction} />);
    
    // Click the button to open the dialog
    await userEvent.click(screen.getByText('Add Transaction'));
    
    // Check if the dialog is opened
    expect(screen.getByText('Add Transaction', { selector: 'h2' })).toBeInTheDocument();
    expect(screen.getByText('Enter the details of your transaction below.')).toBeInTheDocument();
  });
  
  it('validates required fields', async () => {
    render(<TransactionForm onAddTransaction={mockOnAddTransaction} />);
    
    // Open the dialog
    await userEvent.click(screen.getByText('Add Transaction'));
    
    // Submit the form without filling any fields
    await userEvent.click(screen.getByRole('button', { name: 'Add Transaction', exact: true }));
    
    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText('Amount must be greater than 0')).toBeInTheDocument();
      expect(screen.getByText('Category is required')).toBeInTheDocument();
    });
    
    // Ensure the onAddTransaction callback was not called
    expect(mockOnAddTransaction).not.toHaveBeenCalled();
  });
  
  it('submits the form with valid data', async () => {
    render(<TransactionForm onAddTransaction={mockOnAddTransaction} />);
    
    // Open the dialog
    await userEvent.click(screen.getByText('Add Transaction'));
    
    // Fill the form
    await userEvent.type(screen.getByPlaceholderText('0.00'), '100');
    
    // Select transaction type
    const typeSelect = screen.getByRole('combobox', { name: /type/i });
    await userEvent.click(typeSelect);
    await userEvent.click(screen.getByRole('option', { name: 'Income' }));
    
    // Select category
    const categorySelect = screen.getByRole('combobox', { name: /category/i });
    await userEvent.click(categorySelect);
    await userEvent.click(screen.getByRole('option', { name: 'Salary' }));
    
    // Add description
    await userEvent.type(screen.getByPlaceholderText('Enter transaction details'), 'Monthly salary');
    
    // Submit the form
    await userEvent.click(screen.getByRole('button', { name: 'Add Transaction', exact: true }));
    
    // Check if the onAddTransaction callback was called with the correct data
    await waitFor(() => {
      expect(mockOnAddTransaction).toHaveBeenCalledWith({
        id: 'test-uuid',
        date: '2025-05-22',
        description: 'Monthly salary',
        category: 'Salary',
        amount: 100,
        type: 'income'
      });
    });
  });
  
  it('resets the form after submission', async () => {
    render(<TransactionForm onAddTransaction={mockOnAddTransaction} />);
    
    // Open the dialog
    await userEvent.click(screen.getByText('Add Transaction'));
    
    // Fill the form
    await userEvent.type(screen.getByPlaceholderText('0.00'), '100');
    
    // Select transaction type
    const typeSelect = screen.getByRole('combobox', { name: /type/i });
    await userEvent.click(typeSelect);
    await userEvent.click(screen.getByRole('option', { name: 'Expense' }));
    
    // Select category
    const categorySelect = screen.getByRole('combobox', { name: /category/i });
    await userEvent.click(categorySelect);
    await userEvent.click(screen.getByRole('option', { name: 'Food & Dining' }));
    
    // Submit the form
    await userEvent.click(screen.getByRole('button', { name: 'Add Transaction', exact: true }));
    
    // Check if the form was reset and closed
    await waitFor(() => {
      expect(mockOnAddTransaction).toHaveBeenCalled();
    });
    
    // Reopen the form to check if it was reset
    await userEvent.click(screen.getByText('Add Transaction'));
    
    // Check if the form fields are reset
    expect(screen.getByPlaceholderText('0.00')).toHaveValue(null);
    expect(screen.getByPlaceholderText('Enter transaction details')).toHaveValue('');
  });
});
