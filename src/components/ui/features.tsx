"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Settings2, <PERSON><PERSON><PERSON>, <PERSON>ap } from 'lucide-react'

export function Features() {
    return (
        <section className="py-12 md:py-20">
            {/* Header Section */}
            <div className="w-full max-w-6xl mx-auto text-center py-16 px-4">
              <h2 className="text-[42px] md:text-[48px] lg:text-[52px] font-[200] leading-tight tracking-[-0.03em] gradient-text bg-gradient-to-r from-white via-cyan-100 to-teal-100">
                Powerful Financial Tools
              </h2>
              <p className="mt-4 text-[16px] md:text-[18px] text-white/70 max-w-2xl mx-auto">
                Cashflowee AI is evolving to be more than just a budget tracker. It supports an entire ecosystem of tools helping users take control of their finances with ease.
              </p>
            </div>
            
            {/* Features Bento Grid Section */}
            <div className="w-full max-w-7xl mx-auto px-4 pb-24">
              {/* Feature Row 1 */}
              <div className="flex flex-col md:flex-row gap-6 mb-6">
                {/* 1/3 Column */}
                <div className="w-full md:w-1/3 glass-effect rounded-2xl p-8 border border-neutral-200/20 shadow-2xl">
                  <div className="icon-circle mb-4">
                    <Zap className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-2xl text-white mb-3">AI-Powered Import</h3>
                  <p className="text-neutral-200/70">Upload bank statements for instant AI processing and categorization.</p>
                </div>
                
                {/* 2/3 Column */}
                <div className="w-full md:w-2/3 glass-effect rounded-2xl p-8 border border-neutral-200/20 flex flex-col md:flex-row items-center shadow-2xl">
                  <div className="flex-1 mb-6 md:mb-0 md:mr-6">
                    <h3 className="text-2xl text-white mb-3">Smart Categorization</h3>
                    <p className="text-neutral-200/70">Automatic spending pattern analysis and transaction categorization with intelligent insights.</p>
                    <ul className="mt-4 space-y-2">
                      <li className="flex items-center text-neutral-200/80 text-sm">
                        <i className="fas fa-check text-white mr-3 w-4"></i>
                        <span>Pattern recognition algorithms</span>
                      </li>
                      <li className="flex items-center text-neutral-200/80 text-sm">
                        <i className="fas fa-check text-white mr-3 w-4"></i>
                        <span>Custom category creation</span>
                      </li>
                      <li className="flex items-center text-neutral-200/80 text-sm">
                        <i className="fas fa-check text-white mr-3 w-4"></i>
                        <span>Intelligent expense tracking</span>
                      </li>
                    </ul>
                  </div>
                  <div className="w-full md:w-64 h-48 bg-black/10 rounded-xl flex items-center justify-center border border-neutral-200/10">
                    <Cpu className="w-20 h-20 text-white/40" />
                  </div>
                </div>
              </div>
              
              {/* Feature Row 2 */}
              <div className="flex flex-col md:flex-row-reverse gap-6 mb-6">
                {/* 1/3 Column */}
                <div className="w-full md:w-1/3 glass-effect rounded-2xl p-8 border border-neutral-200/20 shadow-2xl">
                  <div className="icon-circle mb-4">
                    <Fingerprint className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-2xl text-white mb-3">Secure Data</h3>
                  <p className="text-neutral-200/70">Your financial data stays private and secure with our robust protection.</p>
                </div>
                
                {/* 2/3 Column */}
                <div className="w-full md:w-2/3 glass-effect rounded-2xl p-8 border border-neutral-200/20 flex flex-col md:flex-row items-center shadow-2xl">
                  <div className="flex-1 mb-6 md:mb-0 md:mr-6">
                    <h3 className="text-2xl text-white mb-3">Mobile-First Design</h3>
                    <p className="text-neutral-200/70">Access insights on any device with our responsive interface optimized for all screen sizes.</p>
                    <div className="mt-6 grid grid-cols-2 gap-4">
                      <div className="bg-black/10 rounded-lg p-3 text-center">
                        <div className="text-xl font-[300] text-white">100%</div>
                        <div className="text-xs text-neutral-200/60 mt-1">Responsive</div>
                      </div>
                      <div className="bg-black/10 rounded-lg p-3 text-center">
                        <div className="text-xl font-[300] text-white">Fast</div>
                        <div className="text-xs text-neutral-200/60 mt-1">Performance</div>
                      </div>
                    </div>
                  </div>
                  <div className="w-full md:w-64 h-48 bg-black/10 rounded-xl flex items-center justify-center border border-neutral-200/10">
                    <Settings2 className="w-20 h-20 text-white/40" />
                  </div>
                </div>
              </div>
              
              {/* Feature Row 3 */}
              <div className="flex flex-col md:flex-row gap-6">
                {/* 1/3 Column */}
                <div className="w-full md:w-1/3 glass-effect rounded-2xl p-8 border border-neutral-200/20 shadow-2xl">
                  <div className="icon-circle mb-4">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="text-2xl text-white mb-3">Free to Start</h3>
                  <p className="text-neutral-200/70">Feature-rich free tier with affordable upgrade options.</p>
                </div>
                
                {/* 2/3 Column */}
                <div className="w-full md:w-2/3 glass-effect rounded-2xl p-8 border border-neutral-200/20 shadow-2xl">
                  <h3 className="text-2xl text-white mb-3">Customization & Insights</h3>
                  <p className="text-neutral-200/70 mb-6">Personalize categories and create custom financial reports with powerful analytics tools.</p>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="p-4 bg-black/10 rounded-lg flex flex-col items-center justify-center">
                      <Pencil className="w-8 h-8 text-white/70 mb-2" />
                      <span className="text-sm text-neutral-200/60">Custom</span>
                    </div>
                    <div className="p-4 bg-black/10 rounded-lg flex flex-col items-center justify-center">
                      <i className="fas fa-chart-bar text-2xl text-white/70 mb-2"></i>
                      <span className="text-sm text-neutral-200/60">Reports</span>
                    </div>
                    <div className="p-4 bg-black/10 rounded-lg flex flex-col items-center justify-center">
                      <i className="fas fa-pie-chart text-2xl text-white/70 mb-2"></i>
                      <span className="text-sm text-neutral-200/60">Analytics</span>
                    </div>
                    <div className="p-4 bg-black/10 rounded-lg flex flex-col items-center justify-center">
                      <i className="fas fa-download text-2xl text-white/70 mb-2"></i>
                      <span className="text-sm text-neutral-200/60">Export</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
        </section>
    )
}
