"use client"

import React, { useState } from "react"
import { motion } from "framer-motion"
import Link from "next/link"
import { Home, BarChart2, CreditCard, Settings, HelpCircle, Sparkles, MessageSquare, DollarSign, ArrowRight, LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface NavItem {
  name: string
  url: string
  icon: string
}

// Map of icon names to icon components
const iconMap: Record<string, LucideIcon> = {
  "home": Home,
  "dashboard": BarChart2,
  "transactions": CreditCard,
  "settings": Settings,
  "help": HelpCircle,
  "features": Sparkles,
  "testimonials": MessageSquare,
  "pricing": DollarSign,
  "cta": ArrowRight
}

interface NavBarProps {
  items: NavItem[]
  className?: string
}

export function NavBar({ items, className }: NavBarProps) {
  const [activeTab, setActiveTab] = useState(items[0].name)

  return (
    <div
      className={cn(
        "fixed bottom-0 sm:top-0 left-1/2 -translate-x-1/2 z-50 mb-6 sm:pt-6 pointer-events-none",
        className,
      )}
    >
      <div className="flex items-center gap-3 glass-effect border border-neutral-200/20 backdrop-blur-lg py-1 px-1 rounded-full shadow-2xl pointer-events-auto">
        {items.map((item) => {
          const Icon = iconMap[item.icon.toLowerCase()] || Home
          const isActive = activeTab === item.name

          return (
            <Link
              key={item.name}
              href={item.url}
              onClick={() => setActiveTab(item.name)}
              className={cn(
                "relative cursor-pointer text-sm font-semibold px-6 py-2 rounded-full transition-colors",
                "text-white hover:text-[#10b981]",
                isActive && "bg-black/20 text-[#10b981]",
                "pointer-events-auto"
              )}
            >
              <span className="hidden md:inline">{item.name}</span>
              <span className="md:hidden">
                <Icon size={18} strokeWidth={2.5} />
              </span>
              {isActive && (
                <motion.div
                  layoutId="lamp"
                  className="absolute inset-0 w-full bg-[#10b981]/10 rounded-full -z-10"
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                  }}
                >
                  <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-[#10b981] rounded-t-full">
                    <div className="absolute w-12 h-6 bg-[#10b981]/20 rounded-full blur-md -top-2 -left-2" />
                    <div className="absolute w-8 h-6 bg-[#10b981]/20 rounded-full blur-md -top-1" />
                    <div className="absolute w-4 h-4 bg-[#10b981]/20 rounded-full blur-sm top-0 left-2" />
                  </div>
                </motion.div>
              )}
            </Link>
          )
        })}
      </div>
    </div>
  )
}
