'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Upload, Shield, CreditCard, Check } from 'lucide-react';

export function BankStatementUploadPreview() {
  return (
    <div className="w-full max-w-4xl mx-auto glass-effect rounded-2xl shadow-2xl p-8 md:p-12 border border-neutral-200/20">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl md:text-3xl text-white mb-3">
          Upload Your Bank Statement
        </h2>
      </div>

      {/* Upload Area */}
      <div className="relative">
        {/* Dashed border upload zone */}
        <div className="border-2 border-dashed border-neutral-200/30 rounded-xl p-12 md:p-16 text-center bg-black/10 hover:bg-black/20 transition-colors">
          {/* Upload icon */}
          <div className="icon-circle mx-auto mb-6">
            <Upload className="w-6 h-6 text-white" />
          </div>
          
          {/* Upload text */}
          <p className="text-lg text-white mb-4">
            Drag & Drop Bank Statement Here
          </p>
          <p className="text-sm text-neutral-200/60 mb-8">
            or
          </p>
          
          {/* Browse Files Button */}
          <Button 
            variant="outline" 
            size="lg"
            className="bg-black/20 border-neutral-200/20 text-white hover:bg-black/30 px-8 py-3 rounded-lg font-medium"
          >
            Browse Files
          </Button>
        </div>
        
        {/* Categorize Button */}
        <div className="mt-8 text-center">
          <Button 
            size="lg"
            className="bg-[#10b981] hover:bg-[#0d9468] text-white px-12 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all"
          >
            Categorize Bank Statement →
          </Button>
        </div>
      </div>

      {/* Security notice */}
      <div className="mt-8 text-center">
        <p className="text-sm text-neutral-200/60 mb-6">
          Your data is processed securely and never shared with third parties
        </p>
        
        {/* Security badges */}
        <div className="flex flex-wrap justify-center items-center gap-6 md:gap-8">
          {/* Bank-Level Security */}
          <div className="flex items-center gap-2 text-sm text-neutral-200/80">
            <div className="icon-circle">
              <Shield className="w-4 h-4 text-white" />
            </div>
            <span className="font-medium">Bank-Level Security</span>
          </div>
          
          {/* Works with All Major Banks */}
          <div className="flex items-center gap-2 text-sm text-neutral-200/80">
            <div className="icon-circle">
              <CreditCard className="w-4 h-4 text-white" />
            </div>
            <span className="font-medium">Works with All Major Banks</span>
          </div>
          
          {/* GDPR Compliant */}
          <div className="flex items-center gap-2 text-sm text-neutral-200/80">
            <div className="icon-circle">
              <Check className="w-4 h-4 text-white" />
            </div>
            <span className="font-medium">GDPR Compliant</span>
          </div>
        </div>
      </div>
    </div>
  );
} 