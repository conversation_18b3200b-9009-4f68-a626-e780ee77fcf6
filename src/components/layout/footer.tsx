'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';

export function Footer() {
  return (
    <footer className="bg-gradient-to-br from-[#141e30] to-[#244045] py-12 md:py-16 border-t border-[#244045]/50">
      <div className="container px-4 md:px-6 mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-12">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <h2 className="text-xl font-bold text-white">Cashflowee AI</h2>
              <div className="ml-2 w-2 h-2 rounded-full bg-[#10b981] shadow-[0_0_8px_rgba(16,185,129,0.8)]"></div>
            </div>
            <p className="text-white/70 mb-6 max-w-md">
              Simplify your personal finances with AI-powered bank statement import, automatic categorization, and a beautiful mobile-friendly experience.
            </p>
            <div className="flex space-x-4">
              <Link href="#" aria-label="Twitter">
                <Button variant="ghost" size="icon" className="text-white/70 hover:text-white hover:bg-[#10b981]/20 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-twitter">
                    <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                  </svg>
                </Button>
              </Link>
              <Link href="#" aria-label="GitHub">
                <Button variant="ghost" size="icon" className="text-white/70 hover:text-white hover:bg-[#10b981]/20 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-github">
                    <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path>
                    <path d="M9 18c-4.51 2-5-2-7-2"></path>
                  </svg>
                </Button>
              </Link>
              <Link href="#" aria-label="LinkedIn">
                <Button variant="ghost" size="icon" className="text-white/70 hover:text-white hover:bg-[#10b981]/20 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-linkedin">
                    <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                    <rect width="4" height="12" x="2" y="9"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                </Button>
              </Link>
            </div>
          </div>
          
          {/* Quick Links */}
          <div className="col-span-1">
            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link href="#home" className="text-white/70 hover:text-[#10b981] transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="#features" className="text-white/70 hover:text-[#10b981] transition-colors">
                  Features
                </Link>
              </li>
              <li>
                <Link href="#pricing" className="text-white/70 hover:text-[#10b981] transition-colors">
                  Pricing
                </Link>
              </li>
              <li>
                <Link href="#demo" className="text-white/70 hover:text-[#10b981] transition-colors">
                  Demo
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Legal */}
          <div className="col-span-1">
            <h3 className="text-white font-semibold mb-4">Legal</h3>
            <ul className="space-y-3">
              <li>
                <Link href="#" className="text-white/70 hover:text-[#10b981] transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="#" className="text-white/70 hover:text-[#10b981] transition-colors">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="#" className="text-white/70 hover:text-[#10b981] transition-colors">
                  Cookie Policy
                </Link>
              </li>
              <li>
                <Link href="#" className="text-white/70 hover:text-[#10b981] transition-colors">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-[#244045]/50 mt-12 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-white/60">
            &copy; {new Date().getFullYear()} Cashflowee AI. All rights reserved.
          </p>
          <div className="mt-4 md:mt-0">
            <Link href="/login">
              <Button 
                variant="outline" 
                className="bg-transparent border border-[#10b981]/50 text-[#10b981] hover:bg-[#10b981]/10 text-sm rounded-full">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
} 