'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { supabase } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';

export function Navbar() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    async function getUser() {
      const { data } = await supabase.auth.getUser();
      setUser(data.user);
      setIsLoading(false);
    }
    
    getUser();
    
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user || null);
      }
    );
    
    return () => {
      if (authListener && authListener.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, []);
  
  async function handleSignOut() {
    await supabase.auth.signOut();
  }
  
  if (isLoading) {
    return (
      <nav className="bg-transparent z-10 relative">
        <div className="container max-w-6xl mx-auto flex h-20 items-center justify-between px-4">
          <div className="flex items-center gap-6 md:gap-10">
            <Link href="/" className="flex items-center gap-2">
              <Image 
                src="/logo.svg" 
                alt="Cashflowee Logo" 
                width={40}
                height={40}
                priority
              />
              <span className="text-xl font-bold text-black">
                Cashflowee
              </span>
            </Link>
          </div>
        </div>
      </nav>
    );
  }
  
  return (
    <nav className="bg-transparent z-10 relative">
      <div className="container max-w-6xl mx-auto flex h-20 items-center justify-between px-4">
        <div className="flex items-center gap-6 md:gap-10">
          <Link href="/" className="flex items-center gap-2">
            <Image 
              src="/logo.svg" 
              alt="Cashflowee Logo" 
              width={40}
              height={40}
              priority
            />
            <span className="text-xl font-bold text-black">
              Cashflowee
            </span>
          </Link>
        </div>
        
        <div className="flex items-center gap-4">
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full cursor-pointer bg-[#10b981] hover:bg-[#10b981]/50">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-[#10b981] text-white">
                      {user.email?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-[#141e30] border-[#244045] text-white">
                <DropdownMenuItem className="hover:bg-[#244045] focus:bg-[#244045] cursor-pointer">
                  <Link href="/dashboard" className="w-full">Dashboard</Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleSignOut} className="hover:bg-[#244045] focus:bg-[#244045] cursor-pointer">
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Link href="/login">
              <Button 
                size="lg" 
                className="relative cursor-pointer text-sm font-semibold px-6 py-2 rounded-full transition-colors bg-[#244045]/80 text-[#10b981] hover:bg-[#10b981] hover:text-[#fff] shadow-lg">
                <span>Sign In</span>
                <div className="absolute inset-0 w-full bg-[#10b981]/10 rounded-full -z-10">
                  <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-[#10b981] rounded-t-full">
                    <div className="absolute w-12 h-6 bg-[#10b981]/20 rounded-full blur-md -top-2 -left-2" />
                    <div className="absolute w-8 h-6 bg-[#10b981]/20 rounded-full blur-md -top-1" />
                    <div className="absolute w-4 h-4 bg-[#10b981]/20 rounded-full blur-sm top-0 left-2" />
                  </div>
                </div>
              </Button>
            </Link>
          )}
        </div>
      </div>
    </nav>
  );
} 