'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { supabase } from '@/lib/supabase';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
});

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoginForm, setIsLoginForm] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  const router = useRouter();

  // Check if user is already logged in
  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        
        if (data.session) {
          console.log("User already has a session, redirecting to dashboard");
          router.replace('/dashboard');
        }
      } catch (err) {
        console.error("Error checking session:", err);
      }
    };
    
    checkSession();
  }, [router]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  async function onSubmit(data: z.infer<typeof formSchema>) {
    setIsLoading(true);
    setErrorMessage('');
    
    try {
      if (isLoginForm) {
        // Sign in
        console.log("Attempting sign in with:", data.email);
        
        const { data: authData, error } = await supabase.auth.signInWithPassword({
          email: data.email,
          password: data.password,
        });
        
        if (error) throw error;
        
        console.log("Sign in successful:", !!authData.session);
        
        if (authData.session) {
          // Use hard navigation to ensure cookies are properly set
          window.location.href = '/dashboard';
        } else {
          throw new Error('No session was created after login');
        }
      } else {
        // Sign up
        console.log("Attempting sign up with:", data.email);
        
        const { data: authData, error } = await supabase.auth.signUp({
          email: data.email,
          password: data.password,
          options: {
            // When set to true, automatically signs in the user on successful signup
            emailRedirectTo: `${window.location.origin}/auth/callback`
          }
        });
        
        if (error) throw error;
        
        if (authData.session) {
          console.log("Sign up successful with immediate session, redirecting");
          // Use hard navigation to ensure cookies are properly set
          window.location.href = '/dashboard';
        } else {
          console.log("Sign up successful, but email confirmation required");
          setErrorMessage('Registration successful! Please check your email for confirmation link before logging in.');
          setIsLoginForm(true);
        }
      }
    } catch (error: unknown) {
      const errorMsg = error instanceof Error ? error.message : 'Authentication failed. Please try again.';
      console.error("Auth error:", errorMsg);
      setErrorMessage(errorMsg);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <>
      <style jsx global>{`
        .auth-card:hover {
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.15) !important;
        }
      `}</style>
      <div className="relative min-h-screen flex items-center justify-center py-12 overflow-x-hidden">
        {/* Clean white background */}
        <div className="fixed inset-0 bg-white z-0"></div>
        
      <Card className="relative z-10 w-full max-w-md bg-white border border-gray-100 rounded-lg transition-all duration-300 hover:translate-y-[-2px] auth-card" style={{ 
        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)'
      }}>
        <CardHeader className="space-y-2">
          <div className="flex justify-center mb-6">
            <Image 
              src="/logo.svg" 
              alt="Cashflowee Logo" 
              width={80}
              height={80}
              priority
              className="drop-shadow-lg"
            />
          </div>
          <CardTitle className="text-2xl text-center text-gray-800">
            {isLoginForm ? 'Welcome Back' : 'Create Your Account'}
          </CardTitle>
          <CardDescription className="text-center text-gray-500">
            {isLoginForm
              ? 'Enter your email and password to access your account'
              : 'Create a new account to get started with Cashflowee AI'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Email</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        {...field} 
                        className="bg-white border-gray-200 text-gray-800 placeholder:text-gray-400 focus:border-[#10b981] focus:ring-[#10b981]/20 rounded-md"
                      />
                    </FormControl>
                    <FormMessage className="text-[#10b981]" />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">Password</FormLabel>
                    <FormControl>
                      <Input 
                        type="password" 
                        placeholder="••••••••" 
                        {...field} 
                        className="bg-white border-gray-200 text-gray-800 placeholder:text-gray-400 focus:border-[#10b981] focus:ring-[#10b981]/20 rounded-md"
                      />
                    </FormControl>
                    <FormMessage className="text-[#10b981]" />
                  </FormItem>
                )}
              />

              {errorMessage && (
                <div className="text-sm text-red-600 mt-2 p-4 bg-red-50 rounded-md border border-red-100">
                  {errorMessage}
                </div>
              )}

              <Button 
                type="submit" 
                className="relative w-full cursor-pointer bg-[#10b981] hover:bg-[#0ea271] text-white text-sm font-semibold px-6 py-3 rounded-md transition-all duration-300" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <span className="w-4 h-4 mr-2 rounded-full border-2 border-t-[#10b981] border-r-[#10b981]/40 border-b-[#10b981]/70 border-l-[#10b981]/10 animate-spin"></span>
                    Processing...
                  </span>
                ) : (
                  <>
                    <span>{isLoginForm ? 'Sign In' : 'Create Account'}</span>

                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4 border-t border-gray-100 pt-6">
          <Button
            variant="link"
            className="w-full text-[#10b981] hover:text-[#0ea271] cursor-pointer"
            onClick={() => setIsLoginForm(!isLoginForm)}
          >
            {isLoginForm
              ? "Don't have an account? Sign Up"
              : 'Already have an account? Sign In'}
          </Button>
          <div className="text-center text-sm text-gray-500">
            <Link href="/" className="hover:underline text-[#10b981] hover:text-[#0ea271] transition-colors">
              Back to Home
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
    </>
  );
} 