'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Download, FileText, Database, Calendar, Filter } from 'lucide-react';

export default function ExportPage() {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data } = await supabase.auth.getSession();
        
        if (!data.session) {
          router.replace('/login');
          return;
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error("Auth check error:", err);
        router.replace('/login');
      }
    };
    
    checkAuth();
  }, [router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin mx-auto text-slate-600">⟳</div>
          <p className="mt-2 text-slate-600">Loading export...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
          <Download className="h-8 w-8 text-emerald-600" />
          Export Data
        </h1>
        <p className="mt-2 text-gray-600">
          Export your financial data in various formats for backup or analysis.
        </p>
      </div>

      {/* Export Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-emerald-600" />
            </div>
            <h3 className="font-semibold text-gray-900">CSV Export</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Export transaction data as CSV for use in spreadsheet applications.
          </p>
          <button className="w-full bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
            Export CSV
          </button>
        </div>

        <div className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Database className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900">Excel Export</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Export data as Excel workbook with multiple sheets and formatting.
          </p>
          <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Export Excel
          </button>
        </div>

        <div className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <FileText className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900">PDF Report</h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Generate a formatted PDF report with charts and summaries.
          </p>
          <button className="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
            Export PDF
          </button>
        </div>
      </div>

      {/* Export Filters */}
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Export Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <select className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <option>All Time</option>
                <option>This Month</option>
                <option>Last Month</option>
                <option>Last 3 Months</option>
                <option>This Year</option>
                <option>Custom Range</option>
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Categories</label>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                <option>All Categories</option>
                <option>Income Only</option>
                <option>Expenses Only</option>
                <option>Food & Dining</option>
                <option>Transportation</option>
                <option>Shopping</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="mt-4 flex items-center gap-4">
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500" defaultChecked />
            <span className="text-sm text-gray-700">Include transaction descriptions</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500" defaultChecked />
            <span className="text-sm text-gray-700">Include category information</span>
          </label>
        </div>
      </div>

      {/* Recent Exports */}
      <div className="bg-white border border-gray-200 rounded-xl overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Exports</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          <div className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">November_2024_Transactions.csv</h4>
                <p className="text-sm text-gray-500">Exported 2 days ago • 156 KB • 42 transactions</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Download className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Database className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Q4_2024_Financial_Report.xlsx</h4>
                <p className="text-sm text-gray-500">Exported 1 week ago • 523 KB • Full quarter data</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Download className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Info */}
      <div className="bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200 rounded-xl p-6">
        <div className="flex items-start gap-4">
          <div className="w-8 h-8 bg-emerald-200 rounded-lg flex items-center justify-center flex-shrink-0">
            <Download className="h-4 w-4 text-emerald-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Export Information</h3>
            <div className="space-y-1 text-sm text-gray-600">
              <p>• All exports are processed securely and encrypted</p>
              <p>• Files are automatically deleted after 30 days for security</p>
              <p>• Large exports may take a few minutes to process</p>
              <p>• You can download the same export multiple times within 30 days</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 