'use client';

import { useState, useRef } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Toaster } from '@/components/ui/toaster';
import { 
  Upload,
  TrendingUp,
  Shield,
  Zap
} from 'lucide-react';

export default function DashboardPage() {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  
  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  };

  const handleFiles = (files: FileList) => {
    console.log('Files selected:', files);
    toast({
      title: "Files selected",
      description: `${files.length} file(s) selected for upload!`,
    });
    // File processing would happen here in the full implementation
  };
  
  return (
    <div className="w-full min-h-screen flex">
      <Toaster />
      
      {/* Main Content */}
      <div className="flex-1 flex min-h-screen w-full">
        {/* Left Side - Upload Section */}
        <div className="flex-1 w-full lg:w-3/5 flex items-center justify-center p-12">
          <div className="w-full max-w-2xl">
            <div className="mb-8 text-left">
              <h1 className="text-gray-900 text-4xl font-bold leading-10 mb-4">
                Upload Your Bank Statement
              </h1>
              <p className="text-gray-600 text-lg leading-7">
                Get started with Cashflowee AI by uploading your bank statement.
                We&apos;ll analyze your financial data and provide valuable insights.
              </p>
            </div>

            <div 
              className={`relative bg-white border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200 shadow-sm hover:shadow-md ${
                isDragOver 
                  ? 'border-emerald-600 bg-emerald-50 transform scale-105' 
                  : 'border-gray-300 hover:border-emerald-600'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                accept=".pdf,.xls,.xlsx,.csv"
                multiple
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />

              <div className="flex flex-col items-center justify-center">
                <div className={`p-6 rounded-full transition-colors duration-200 ${
                  isDragOver ? 'bg-emerald-200' : 'bg-emerald-100'
                }`}>
                  <Upload className="h-12 w-12 text-emerald-600 stroke-2" />
                </div>

                <div className="mt-6">
                  <h3 className="text-gray-900 text-xl font-semibold leading-7">
                    Upload your bank statement
                  </h3>
                  <p className="text-gray-600 font-normal mt-2">
                    Drag and drop your files or{' '}
                    <span className="text-emerald-600 font-medium underline ml-1">
                      click to browse
                    </span>
                  </p>
                </div>

                <div className="text-gray-500 text-sm mt-6">
                  <p className="font-medium mb-2">Supported formats:</p>
                  <div className="flex flex-wrap gap-2 justify-center mb-2">
                    <span className="bg-emerald-600 text-white text-xs font-medium px-3 py-1 rounded-full">
                      PDF
                    </span>
                    <span className="bg-emerald-600 text-white text-xs font-medium px-3 py-1 rounded-full">
                      Excel (XLS/XLSX)
                    </span>
                    <span className="bg-emerald-600 text-white text-xs font-medium px-3 py-1 rounded-full">
                      CSV
                    </span>
                  </div>
                  <p className="text-xs mt-2">Maximum file size: 10MB per file</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Greeting Section */}
        <div className="w-full lg:w-2/5 bg-emerald-50 border-l border-emerald-100 p-12 flex items-center">
          <div className="w-full">
            <div className="mb-8">
              <h3 className="text-gray-900 text-2xl font-semibold leading-8 mb-4">
                Welcome to Cashflowee AI!
              </h3>
              <p className="text-gray-600 text-lg leading-7">
                Please upload your bank statement to get started.
              </p>
            </div>

            {/* Feature Highlights */}
            <div>
              <h4 className="text-gray-900 text-lg font-semibold mb-4">
                What you&apos;ll get:
              </h4>
              <div className="flex flex-col gap-3">
                <div className="bg-white bg-opacity-80 border border-emerald-200 rounded-lg p-4 shadow-sm">
                  <div className="flex items-center">
                    <div className="p-2 bg-emerald-200 rounded-lg mr-3">
                      <TrendingUp className="h-5 w-5 text-emerald-600 stroke-2" />
                    </div>
                    <div className="flex-1">
                      <h5 className="text-gray-900 font-medium">Financial Insights</h5>
                      <p className="text-gray-600 text-sm leading-5">
                        Detailed analysis of your spending patterns
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white bg-opacity-80 border border-emerald-200 rounded-lg p-4 shadow-sm">
                  <div className="flex items-center">
                    <div className="p-2 bg-emerald-200 rounded-lg mr-3">
                      <Shield className="h-5 w-5 text-emerald-600 stroke-2" />
                    </div>
                    <div className="flex-1">
                      <h5 className="text-gray-900 font-medium">Secure Processing</h5>
                      <p className="text-gray-600 text-sm leading-5">
                        Your data is encrypted and protected
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white bg-opacity-80 border border-emerald-200 rounded-lg p-4 shadow-sm">
                  <div className="flex items-center">
                    <div className="p-2 bg-emerald-200 rounded-lg mr-3">
                      <Zap className="h-5 w-5 text-emerald-600 stroke-2" />
                    </div>
                    <div className="flex-1">
                      <h5 className="text-gray-900 font-medium">Quick Results</h5>
                      <p className="text-gray-600 text-sm leading-5">
                        Get your report in minutes
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Responsive Design - Mobile Layout */}
      <style jsx>{`
        @media (max-width: 1024px) {
          .flex {
            flex-direction: column;
          }
          .w-full.lg\\:w-3\\/5,
          .w-full.lg\\:w-2\\/5 {
            width: 100%;
          }
          .w-full.lg\\:w-2\\/5 {
            border-left: none;
            border-top: 1px solid rgb(167 243 208);
          }
        }
      `}</style>
    </div>
  );
}
