'use client';

import { BarChart3, TrendingUp, DollarSign, CreditCard, Target } from 'lucide-react';

export default function DashboardPage() {
  return (
    <div className="w-full min-h-screen">
      <div className="p-6 space-y-6 w-full">
        {/* Header */}
        <div className="border-b border-gray-200 pb-4">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <BarChart3 className="h-8 w-8 text-emerald-600" />
            Analytics & Insights
          </h1>
          <p className="mt-2 text-gray-600">
            Get detailed insights into your financial patterns and trends.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm font-medium">Total Balance</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">$12,450</p>
                <p className="text-emerald-600 text-sm mt-1 flex items-center gap-1">
                  <TrendingUp className="h-4 w-4" />
                  +8.2% from last month
                </p>
              </div>
              <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm font-medium">Monthly Income</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">$5,200</p>
                <p className="text-emerald-600 text-sm mt-1 flex items-center gap-1">
                  <TrendingUp className="h-4 w-4" />
                  +3.1% from last month
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm font-medium">Monthly Expenses</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">$3,850</p>
                <p className="text-red-600 text-sm mt-1 flex items-center gap-1">
                  <TrendingUp className="h-4 w-4 rotate-180" />
                  +12.5% from last month
                </p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <CreditCard className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm font-medium">Savings Rate</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">26%</p>
                <p className="text-emerald-600 text-sm mt-1 flex items-center gap-1">
                  <Target className="h-4 w-4" />
                  Above target (20%)
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Target className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 w-full">
          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Spending by Category</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-blue-500 rounded"></div>
                  <span className="text-gray-700">Food & Dining</span>
                </div>
                <span className="font-medium text-gray-900">$1,200 (31%)</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-emerald-500 rounded"></div>
                  <span className="text-gray-700">Transportation</span>
                </div>
                <span className="font-medium text-gray-900">$850 (22%)</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                  <span className="text-gray-700">Shopping</span>
                </div>
                <span className="font-medium text-gray-900">$680 (18%)</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-purple-500 rounded"></div>
                  <span className="text-gray-700">Entertainment</span>
                </div>
                <span className="font-medium text-gray-900">$520 (13%)</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span className="text-gray-700">Bills & Utilities</span>
                </div>
                <span className="font-medium text-gray-900">$600 (16%)</span>
              </div>
            </div>
          </div>

          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Trends</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-emerald-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">November 2024</p>
                  <p className="text-sm text-gray-600">Net: +$1,350</p>
                </div>
                <div className="text-right">
                  <p className="text-emerald-600 font-semibold">Income: $5,200</p>
                  <p className="text-red-600 font-semibold">Expenses: $3,850</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">October 2024</p>
                  <p className="text-sm text-gray-600">Net: +$1,180</p>
                </div>
                <div className="text-right">
                  <p className="text-emerald-600 font-semibold">Income: $5,050</p>
                  <p className="text-red-600 font-semibold">Expenses: $3,870</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">September 2024</p>
                  <p className="text-sm text-gray-600">Net: +$980</p>
                </div>
                <div className="text-right">
                  <p className="text-emerald-600 font-semibold">Income: $4,800</p>
                  <p className="text-red-600 font-semibold">Expenses: $3,820</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Coming Soon */}
        <div className="bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200 rounded-xl p-8 text-center w-full">
          <div className="max-w-2xl mx-auto">
            <BarChart3 className="h-16 w-16 text-emerald-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Advanced Analytics Coming Soon
            </h2>
            <p className="text-gray-600 mb-6">
              We&apos;re working on bringing you more detailed analytics including interactive charts,
              predictive insights, and custom reporting features.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-left">
              <div className="bg-white p-4 rounded-lg border border-emerald-200">
                <h3 className="font-semibold text-gray-900 mb-2">Interactive Charts</h3>
                <p className="text-sm text-gray-600">Dynamic visualizations with drill-down capabilities</p>
              </div>
              <div className="bg-white p-4 rounded-lg border border-emerald-200">
                <h3 className="font-semibold text-gray-900 mb-2">Predictive Insights</h3>
                <p className="text-sm text-gray-600">AI-powered forecasting and trend analysis</p>
              </div>
              <div className="bg-white p-4 rounded-lg border border-emerald-200">
                <h3 className="font-semibold text-gray-900 mb-2">Custom Reports</h3>
                <p className="text-sm text-gray-600">Personalized reports based on your goals</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
