'use client';

import { FileText, Upload, Download, Trash2, Eye, Clock } from 'lucide-react';

export default function StatementsPage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <FileText className="h-8 w-8 text-emerald-600" />
            Bank Statements
          </h1>
          <p className="mt-2 text-gray-600">
            Upload and manage your bank statements for automatic transaction import.
          </p>
        </div>
        <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center gap-2">
          <Upload className="h-4 w-4" />
          Upload Statement
        </button>
      </div>

      {/* Upload Area */}
      <div className="bg-white border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-emerald-400 transition-colors">
        <div className="max-w-md mx-auto">
          <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Upload Bank Statement
          </h3>
          <p className="text-gray-600 mb-4">
            Drag and drop your bank statement files or click to browse.
          </p>
          <div className="flex flex-wrap gap-2 justify-center mb-4">
            <span className="bg-emerald-100 text-emerald-800 text-xs font-medium px-3 py-1 rounded-full">
              PDF
            </span>
            <span className="bg-emerald-100 text-emerald-800 text-xs font-medium px-3 py-1 rounded-full">
              CSV
            </span>
            <span className="bg-emerald-100 text-emerald-800 text-xs font-medium px-3 py-1 rounded-full">
              Excel
            </span>
          </div>
          <button className="bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
            Choose Files
          </button>
        </div>
      </div>

      {/* Recent Uploads */}
      <div className="bg-white border border-gray-200 rounded-xl overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Uploads</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {/* Sample statement files */}
          <div className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">November_2024_Statement.pdf</h4>
                <p className="text-sm text-gray-500">Uploaded 2 days ago • 245 KB • 42 transactions</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="px-2 py-1 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full">
                Processed
              </span>
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Eye className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Download className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100">
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">October_2024_Statement.csv</h4>
                <p className="text-sm text-gray-500">Uploaded 1 week ago • 152 KB • 38 transactions</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="px-2 py-1 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full">
                Processed
              </span>
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Eye className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Download className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100">
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">December_2024_Statement.pdf</h4>
                <p className="text-sm text-gray-500">Uploaded 1 hour ago • 198 KB • Processing...</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                Processing
              </span>
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Eye className="h-4 w-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100">
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Info Section */}
      <div className="bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200 rounded-xl p-6">
        <div className="flex items-start gap-4">
          <div className="w-8 h-8 bg-emerald-200 rounded-lg flex items-center justify-center flex-shrink-0">
            <FileText className="h-4 w-4 text-emerald-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Supported File Formats</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p><strong>PDF:</strong> Bank statement PDFs with readable text</p>
              <p><strong>CSV:</strong> Comma-separated values with transaction data</p>
              <p><strong>Excel:</strong> .xlsx and .xls files with transaction records</p>
            </div>
            <p className="text-sm text-gray-500 mt-3">
              Maximum file size: 10MB per file
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 