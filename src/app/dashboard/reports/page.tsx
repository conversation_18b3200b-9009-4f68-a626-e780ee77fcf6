'use client';

import { BarChart3, FileText, Download, Calendar, Filter } from 'lucide-react';

export default function ReportsPage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <BarChart3 className="h-8 w-8 text-emerald-600" />
            Financial Reports
          </h1>
          <p className="mt-2 text-gray-600">
            Generate and view detailed financial reports and insights.
          </p>
        </div>
        <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center gap-2">
          <FileText className="h-4 w-4" />
          Generate Report
        </button>
      </div>

      {/* Report Filters */}
      <div className="bg-white border border-gray-200 rounded-xl p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
              <option>This Month</option>
              <option>Last Month</option>
              <option>Last 3 Months</option>
              <option>This Year</option>
              <option>Custom Range</option>
            </select>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
              <option>All Categories</option>
              <option>Income</option>
              <option>Expenses</option>
              <option>Food & Dining</option>
              <option>Transportation</option>
            </select>
          </div>
          <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
            Apply Filters
          </button>
        </div>
      </div>

      {/* Quick Reports */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Monthly Summary</h3>
            <Download className="h-4 w-4 text-gray-400" />
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Complete overview of income and expenses for the current month.
          </p>
          <button className="text-emerald-600 text-sm font-medium hover:text-emerald-700">
            Generate →
          </button>
        </div>

        <div className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Category Breakdown</h3>
            <Download className="h-4 w-4 text-gray-400" />
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Detailed analysis of spending by category with percentages.
          </p>
          <button className="text-emerald-600 text-sm font-medium hover:text-emerald-700">
            Generate →
          </button>
        </div>

        <div className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Yearly Overview</h3>
            <Download className="h-4 w-4 text-gray-400" />
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Annual financial summary with trends and comparisons.
          </p>
          <button className="text-emerald-600 text-sm font-medium hover:text-emerald-700">
            Generate →
          </button>
        </div>
      </div>

      {/* Recent Reports */}
      <div className="bg-white border border-gray-200 rounded-xl overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Reports</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          <div className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-emerald-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">November 2024 Monthly Report</h4>
                <p className="text-sm text-gray-500">Generated 2 days ago • PDF • 3 pages</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Download className="h-4 w-4" />
              </button>
            </div>
          </div>

          <div className="px-6 py-4 flex items-center justify-between hover:bg-gray-50">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Q4 2024 Category Analysis</h4>
                <p className="text-sm text-gray-500">Generated 1 week ago • PDF • 5 pages</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                <Download className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon */}
      <div className="bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200 rounded-xl p-8 text-center">
        <div className="max-w-md mx-auto">
          <BarChart3 className="h-16 w-16 text-emerald-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Advanced Reporting Coming Soon
          </h2>
          <p className="text-gray-600 mb-4">
            Interactive charts, custom date ranges, and automated report scheduling are in development.
          </p>
          <div className="bg-white px-4 py-2 rounded-lg inline-block">
            <span className="text-sm font-medium text-emerald-600">Expected: Q1 2024</span>
          </div>
        </div>
      </div>
    </div>
  );
} 