'use client';

import { History, Plus, Filter, Search, Download } from 'lucide-react';

export default function TransactionsPage() {
  return (
    <div className="w-full min-h-screen">
      <div className="p-6 space-y-6 w-full">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 pb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <History className="h-8 w-8 text-emerald-600" />
              Transaction History
            </h1>
            <p className="mt-2 text-gray-600">
              View and manage all your financial transactions.
            </p>
          </div>
          <button className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Transaction
          </button>
        </div>

        {/* Filters and Search */}
        <div className="bg-white border border-gray-200 rounded-xl p-4 w-full">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search transactions..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              />
            </div>
            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="h-4 w-4" />
              Filter
            </button>
            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Download className="h-4 w-4" />
              Export
            </button>
          </div>
        </div>

        {/* Transaction Table */}
        <div className="bg-white border border-gray-200 rounded-xl overflow-hidden w-full">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
          </div>
          
          <div className="overflow-x-auto w-full">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {/* Sample transactions */}
                <tr className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Dec 15, 2024</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Grocery Store</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                      Food & Dining
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">-$120.50</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Expense</td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Dec 14, 2024</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Salary Deposit</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs font-medium bg-emerald-100 text-emerald-800 rounded-full">
                      Income
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-emerald-600">+$3,500.00</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Income</td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Dec 13, 2024</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Gas Station</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                      Transportation
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">-$45.20</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Expense</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Empty State or More Info */}
        <div className="bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200 rounded-xl p-8 text-center w-full">
          <div className="max-w-md mx-auto">
            <History className="h-16 w-16 text-emerald-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Complete Transaction Management
            </h2>
            <p className="text-gray-600 mb-4">
              Upload bank statements to automatically import and categorize your transactions, or add them manually.
            </p>
            <button className="bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
              Upload Bank Statement
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 