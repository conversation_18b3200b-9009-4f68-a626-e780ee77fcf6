@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Modern Landing Page Styles */
body, html { 
  font-family: 'Inter', system-ui, sans-serif;
  background: #0b3a4a; 
  margin: 0; 
  padding: 0; 
  min-height: 100vh;
}

h1, h2, h3, h4 {
  font-family: var(--font-manrope), sans-serif !important;
  font-weight: 200 !important;
  letter-spacing: -0.03em !important;
}

.glass-effect {
  backdrop-filter: blur(14px) brightness(0.95);
  -webkit-backdrop-filter: blur(14px) brightness(0.95);
  background-color: rgba(255, 255, 255, 0.08);
}

.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.card-divider {
  height: 1px;
  background-image: linear-gradient(90deg,
    transparent,
    rgba(229, 231, 235, 0.09) 20%,
    rgba(229, 231, 235, 0.22) 50%,
    rgba(229, 231, 235, 0.09) 80%,
    transparent
  );
}

.icon-circle {
  height: 2rem;
  width: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.06);
  border: 1px solid rgba(229, 231, 235, 0.3);
}

.pricing-badge {
  position: absolute;
  top: -2px;
  right: 30px;
  padding: 4px 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 12px;
  font-weight: 500;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

/* @theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
} */

:root {
  --background: oklch(0.98 0.00 56.38);
  --foreground: oklch(0 0 0);
  --card: oklch(0.98 0.00 56.38);
  --card-foreground: oklch(0 0 0);
  --popover: oklch(0.98 0.00 56.38);
  --popover-foreground: oklch(0.22 0 0);
  --primary: oklch(0.76 0.16 62.93);
  --primary-foreground: oklch(1.00 0 0);
  --secondary: oklch(0.83 0.16 82.24);
  --secondary-foreground: oklch(1.00 0 0);
  --muted: oklch(0.94 0.01 53.44);
  --muted-foreground: oklch(0.35 0 0);
  --accent: oklch(0.84 0.13 76.12);
  --accent-foreground: oklch(1.00 0 0);
  --destructive: oklch(0.44 0.16 26.90);
  --destructive-foreground: oklch(1.00 0 0);
  --border: oklch(0.94 0.03 80.99);
  --input: oklch(0.94 0.03 80.99);
  --ring: oklch(0.47 0.15 24.94);
  --chart-1: oklch(0.76 0.16 62.93);
  --chart-2: oklch(0.55 0.24 321.97);
  --chart-3: oklch(0.84 0.13 76.12);
  --chart-4: oklch(0.84 0.13 76.12);
  --chart-5: oklch(0.84 0.13 76.12);
  --sidebar: oklch(0.94 0.01 53.44);
  --sidebar-foreground: oklch(0.22 0 0);
  --sidebar-primary: oklch(0.76 0.16 62.93);
  --sidebar-primary-foreground: oklch(1.00 0 0);
  --sidebar-accent: oklch(0.96 0.06 95.62);
  --sidebar-accent-foreground: oklch(0.40 0.13 25.72);
  --sidebar-border: oklch(0.94 0.03 80.99);
  --sidebar-ring: oklch(0.47 0.15 24.94);
  --font-sans: Roboto;
  --font-serif: Roboto;
  --font-mono: Roboto;
  --radius: 0rem;
  --shadow-2xs: 1px 1px 16px -2px hsl(0 63% 18% / 0.00);
  --shadow-xs: 1px 1px 16px -2px hsl(0 63% 18% / 0.00);
  --shadow-sm: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 1px 2px -3px hsl(0 63% 18% / 0.00);
  --shadow: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 1px 2px -3px hsl(0 63% 18% / 0.00);
  --shadow-md: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 2px 4px -3px hsl(0 63% 18% / 0.00);
  --shadow-lg: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 4px 6px -3px hsl(0 63% 18% / 0.00);
  --shadow-xl: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 8px 10px -3px hsl(0 63% 18% / 0.00);
  --shadow-2xl: 1px 1px 16px -2px hsl(0 63% 18% / 0.00);
  --shadow-color: hsl(0 63% 18%);
  --shadow-opacity: 0;
  --shadow-blur: 16px;
  --shadow-spread: -2px;
  --shadow-offset-x: 1px;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.22rem;
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.22 0.01 56.04);
  --foreground: oklch(0.97 0.00 106.42);
  --card: oklch(0.27 0.01 34.30);
  --card-foreground: oklch(0.97 0.00 106.42);
  --popover: oklch(0.27 0.01 34.30);
  --popover-foreground: oklch(0.97 0.00 106.42);
  --primary: oklch(0.76 0.16 63.73);
  --primary-foreground: oklch(0.98 0.00 56.38);
  --secondary: oklch(0.83 0.16 82.09);
  --secondary-foreground: oklch(0.96 0.06 95.62);
  --muted: oklch(0.27 0.01 34.30);
  --muted-foreground: oklch(0.87 0.00 56.37);
  --accent: oklch(0.56 0.15 49.00);
  --accent-foreground: oklch(0.96 0.06 95.62);
  --destructive: oklch(0.64 0.21 25.33);
  --destructive-foreground: oklch(1.00 0 0);
  --border: oklch(0.37 0.01 67.56);
  --input: oklch(0.37 0.01 67.56);
  --ring: oklch(0.55 0.24 321.97);
  --chart-1: oklch(0.84 0.16 84.43);
  --chart-2: oklch(0.77 0.16 70.08);
  --chart-3: oklch(0.55 0.24 321.97);
  --chart-4: oklch(0.84 0.16 84.43);
  --chart-5: oklch(0.77 0.16 70.08);
  --sidebar: oklch(0.22 0.01 56.04);
  --sidebar-foreground: oklch(0.97 0.00 106.42);
  --sidebar-primary: oklch(0.77 0.16 70.08);
  --sidebar-primary-foreground: oklch(0.98 0.00 56.38);
  --sidebar-accent: oklch(0.56 0.15 49.00);
  --sidebar-accent-foreground: oklch(0.96 0.06 95.62);
  --sidebar-border: oklch(0.37 0.01 67.56);
  --sidebar-ring: oklch(0.77 0.16 70.08);
  --font-sans: Montserrat, sans-serif;
  --font-serif: Roboto, sans-serif;
  --font-mono: IBM Plex Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 1px 1px 16px -2px hsl(0 63% 18% / 0.00);
  --shadow-xs: 1px 1px 16px -2px hsl(0 63% 18% / 0.00);
  --shadow-sm: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 1px 2px -3px hsl(0 63% 18% / 0.00);
  --shadow: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 1px 2px -3px hsl(0 63% 18% / 0.00);
  --shadow-md: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 2px 4px -3px hsl(0 63% 18% / 0.00);
  --shadow-lg: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 4px 6px -3px hsl(0 63% 18% / 0.00);
  --shadow-xl: 1px 1px 16px -2px hsl(0 63% 18% / 0.00), 1px 8px 10px -3px hsl(0 63% 18% / 0.00);
  --shadow-2xl: 1px 1px 16px -2px hsl(0 63% 18% / 0.00);
  --shadow-color: hsl(0 63% 18%);
  --shadow-opacity: 0;
  --shadow-blur: 16px;
  --shadow-spread: -2px;
  --shadow-offset-x: 1px;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.23rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: Montserrat, sans-serif;
  --font-mono: IBM Plex Mono, monospace;
  --font-serif: Roboto, sans-serif;

  --radius-sm: 0.2rem;
  --radius-md: 0.2rem;
  --radius-lg: 0.2rem;
  --radius-xl: 0.2rem;

  --shadow-2xs: none;
  --shadow-xs: none;
  --shadow-sm: none;
  --shadow: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;
  --shadow-2xl: none;
  --radius: 0rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: 0px;
  --shadow-offset-x: 0px;
  --shadow-spread: 0px;
  --shadow-blur: 0px;
  --shadow-opacity: 0;
  --color-shadow-color: transparent;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}