import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Footer } from '@/components/layout/footer';
import { NavBar } from '@/components/ui/nav-bar';
import { Features } from '@/components/ui/features';
import { BankStatementUploadPreview } from '@/components/ui/bank-statement-upload-preview';

export default function HomePage() {
  return (
    <div className="relative min-h-screen overflow-x-hidden">
      {/* Modern gradient background */}
      <div className="fixed inset-0 bg-gradient-to-br from-cyan-800 via-teal-700 to-cyan-900 z-0"></div>
      <div className="fixed inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjUwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZmlsdGVyIGlkPSJub2lzZSI+PGZlVHVyYnVsZW5jZSB0eXBlPSJmcmFjdGFsTm9pc2UiIGJhc2VGcmVxdWVuY3k9IjAuNjUiIG51bU9jdGF2ZXM9IjMiIHN0aXRjaFRpbGVzPSJzdGl0Y2giLz48ZmVCbGVuZCBtb2RlPSJzY3JlZW4iLz48L2ZpbHRlcj48cmVjdCB3aWR0aD0iNTAwIiBoZWlnaHQ9IjUwMCIgZmlsdGVyPSJ1cmwoI25vaXNlKSIgb3BhY2l0eT0iMC4wNSIvPjwvc3ZnPg==')] opacity-40 z-0"></div>
      
      <div className="relative z-10 w-full min-h-screen">
        <NavBar 
          items={[
            { name: "Home", url: "#home", icon: "home" },
            { name: "Features", url: "#features", icon: "features" },
            { name: "Pricing", url: "#pricing", icon: "pricing" },
            { name: "Get Started", url: "#cta", icon: "cta" }
          ]}
          className="z-50"
        />
        
        {/* Hero Section */}
        <section id="home" className="w-full pt-24 pb-16 md:pt-28 md:pb-20 flex items-center justify-center relative overflow-hidden">
          <div className="container px-4 md:px-6 mx-auto relative z-10">
            <div className="flex flex-col items-center justify-center space-y-6 text-center max-w-3xl mx-auto">
              <div className="space-y-4">
                <h1 className="text-[42px] md:text-[56px] lg:text-[64px] font-[200] leading-tight tracking-[-0.03em] gradient-text bg-gradient-to-r from-white via-cyan-100 to-teal-100">
                  Ditch the Spreadsheet. Automate Your Finances.
                </h1>
                <p className="max-w-[700px] text-white/90 text-lg font-normal md:text-lg mx-auto drop-shadow-sm">
                  Cashflowee AI simplifies personal finance with AI-powered bank statement import, automatic categorization, and a beautiful mobile-friendly experience.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 mt-4 md:mt-6">
                <Link href="/login">
                  <Button 
                    size="lg" 
                    className="relative cursor-pointer text-sm font-semibold px-6 py-2 rounded-full transition-colors bg-[#244045]/80 text-[#10b981] hover:bg-[#10b981] hover:text-[#fff] shadow-lg">
                    <span>Start for Free</span>
                    <div className="absolute inset-0 w-full bg-[#10b981]/10 rounded-full -z-10">
                      <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-[#10b981] rounded-t-full">
                        <div className="absolute w-12 h-6 bg-[#10b981]/20 rounded-full blur-md -top-2 -left-2" />
                        <div className="absolute w-8 h-6 bg-[#10b981]/20 rounded-full blur-md -top-1" />
                        <div className="absolute w-4 h-4 bg-[#10b981]/20 rounded-full blur-sm top-0 left-2" />
                      </div>
                    </div>
                  </Button>
                </Link>
                <Link href="#demo">
                  <Button 
                    size="lg" 
                    variant="outline" 
                    className="bg-white text-[#141e30] hover:text-[#000] cursor-pointer hover:bg-white/90 font-semibold text-sm px-6 py-2 shadow-lg transition-all hover:shadow-xl rounded-full">
                    <span>Watch Demo</span>
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
        
        {/* Hero Component Section */}
        <section className="w-full pb-16 md:pb-24">
          <div className="w-full px-4 md:px-6">
            <BankStatementUploadPreview />
          </div>
        </section>

        {/* Demo Section */}
        <section id="demo" className="w-full py-16 md:py-24">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center justify-center space-y-6 text-center mb-10">
              <div className="space-y-3">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl text-white">
                  See Cashflowee AI in Action
                </h2>
                <p className="max-w-[700px] text-white/80 mx-auto text-lg">
                  Watch how easy it is to import transactions, categorize expenses, and gain insights into your finances.
                </p>
              </div>
            </div>
            <div className="relative w-full max-w-4xl mx-auto aspect-video rounded-lg overflow-hidden shadow-2xl border border-[#244045]/50">
              <div className="absolute inset-0 flex items-center justify-center bg-[#141e30]/90">
                <div className="text-center">
                  <div className="w-20 h-20 rounded-full bg-[#10b981]/20 backdrop-blur-md flex items-center justify-center mx-auto mb-4 cursor-pointer hover:bg-[#10b981]/30 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-[#10b981]" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z" />
                    </svg>
                  </div>
                  <p className="text-white/90 font-medium">Click to play demo video</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features">
          <Features />
        </section>

        {/* Pricing Section with Modern Design */}
        <section id="pricing" className="w-full py-16 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="w-full max-w-6xl mx-auto text-center py-16 px-4">
              <h2 className="text-3xl md:text-4xl lg:text-4xl font-[200] leading-tight tracking-[-0.03em] gradient-text bg-gradient-to-r from-white via-cyan-100 to-teal-100">
                Simple, Affordable Pricing
              </h2>
              <p className="mt-4 text-[16px] md:text-[18px] text-white/70 max-w-2xl mx-auto">
                Choose the plan that works best for your financial journey.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8 max-w-6xl mx-auto">
              {/* Free Tier */}
              <div className="glass-effect rounded-2xl shadow-2xl p-6 flex flex-col h-full relative border border-neutral-200/20">
                <div className="flex items-center mb-4">
                  <div className="icon-circle">
                    <i className="fas fa-rocket text-white text-xs"></i>
                  </div>
                  <h3 className="ml-3 text-xl text-white">Free Tier</h3>
                </div>
                
                <div className="mt-2 mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-[200] text-white">$0</span>
                    <span className="text-sm text-neutral-200/60 ml-2">Forever free</span>
                  </div>
                </div>
                
                <div className="card-divider w-full mb-6"></div>
                
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>AI transaction import</span>
                  </li>
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>Basic categorization</span>
                  </li>
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>Financial dashboard</span>
                  </li>
                </ul>
                
                <div className="mt-auto pt-4">
                  <Link href="/login">
                    <button className="w-full py-3 rounded-xl bg-black/20 hover:bg-black/30 text-white text-sm font-medium transition-all duration-200 border border-neutral-200/20">
                      Get Started
                    </button>
                  </Link>
                </div>
              </div>
              
              {/* Pro Tier */}
              <div className="glass-effect rounded-2xl shadow-2xl p-6 flex flex-col h-full relative z-10 transform scale-105 border border-neutral-200/30">
                <div className="pricing-badge">MOST POPULAR</div>
                
                <div className="flex items-center mb-4">
                  <div className="icon-circle bg-black/20">
                    <i className="fas fa-bolt text-white text-xs"></i>
                  </div>
                  <h3 className="ml-3 text-xl text-white">Pro Tier</h3>
                </div>
                
                <div className="mt-2 mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-[200] text-white">$2.99</span>
                    <span className="text-sm text-neutral-200/60 ml-2">/month</span>
                  </div>
                  <p className="text-neutral-200/60 text-sm mt-1">or $24.99/year</p>
                </div>
                
                <div className="card-divider w-full mb-6"></div>
                
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>Everything in Free</span>
                  </li>
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>Multi-account support</span>
                  </li>
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>Advanced reports</span>
                  </li>
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>Priority support</span>
                  </li>
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>Data export</span>
                  </li>
                </ul>
                
                <div className="mt-auto pt-4">
                  <Link href="/login">
                    <button className="w-full py-3 rounded-xl bg-black text-white text-sm font-medium transition-all duration-200 hover:bg-black/80">
                      Upgrade to Pro
                    </button>
                  </Link>
                </div>
              </div>
              
              {/* Lifetime Deal */}
              <div className="glass-effect rounded-2xl shadow-2xl p-6 flex flex-col h-full relative border border-neutral-200/20">
                <div className="flex items-center mb-4">
                  <div className="icon-circle">
                    <i className="fas fa-building text-white text-xs"></i>
                  </div>
                  <h3 className="ml-3 text-xl text-white">Lifetime Deal</h3>
                </div>
                
                <div className="mt-2 mb-6">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-[200] text-white">$49</span>
                    <span className="text-sm text-neutral-200/60 ml-2">One-time payment</span>
                  </div>
                </div>
                
                <div className="bg-[#10b981]/20 text-white/90 text-sm p-3 rounded-md my-3">
                  Limited time launch promotion
                </div>
                
                <div className="card-divider w-full mb-6"></div>
                
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>All Pro features</span>
                  </li>
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>Never pay again</span>
                  </li>
                  <li className="flex items-center text-neutral-200/80 text-sm">
                    <i className="fas fa-check text-white mr-3 w-4"></i>
                    <span>All future updates</span>
                  </li>
                </ul>
                
                <div className="mt-auto pt-4">
                  <Link href="/login">
                    <button className="w-full py-3 rounded-xl bg-black/20 hover:bg-black/30 text-white text-sm font-medium transition-all duration-200 border border-neutral-200/20">
                      Get Lifetime Access
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* CTA Section */}
        <section id="cta" className="w-full py-16 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6 mx-auto">
            <div className="flex flex-col items-center justify-center space-y-8 text-center max-w-3xl mx-auto">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold tracking-tighter md:text-4xl text-white">
                  Ready to Ditch Your Spreadsheets?
                </h2>
                <p className="max-w-[700px] text-white/80 mx-auto text-lg">
                  Join thousands of users who have simplified their finances with Cashflowee AI. Start for free today.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md mx-auto">
                <Link href="/login" className="flex-1">
                  <Button 
                    size="lg" 
                    className="relative w-full cursor-pointer hover:bg-[#10b981] text-sm font-semibold px-6 py-2 rounded-full transition-colors bg-[#244045]/80 text-[#10b981] hover:text-[#fff] shadow-lg">
                    <span>Get Started Free</span>
                    <div className="absolute inset-0 w-full bg-[#10b981]/10 rounded-full -z-10">
                      <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-[#10b981] rounded-t-full">
                        <div className="absolute w-12 h-6 bg-[#10b981]/20 rounded-full blur-md -top-2 -left-2" />
                        <div className="absolute w-8 h-6 bg-[#10b981]/20 rounded-full blur-md -top-1" />
                        <div className="absolute w-4 h-4 bg-[#10b981]/20 rounded-full blur-sm top-0 left-2" />
                      </div>
                    </div>
                  </Button>
                </Link>
                <Link href="#pricing" className="flex-1">
                  <Button 
                    size="lg" 
                    variant="outline" 
                    className="w-full bg-white text-[#141e30] hover:text-[#000] cursor-pointer hover:bg-white/90 font-semibold text-sm px-6 py-2 shadow-lg transition-all hover:shadow-xl rounded-full">
                    <span>View Pricing</span>
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
        <Footer />
      </div>
    </div>
  );
}
