<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 59.09 71.32">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-2 {
        fill: url(#linear-gradient-2);
      }

      .cls-2, .cls-3 {
        mix-blend-mode: multiply;
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        isolation: isolate;
      }
    </style>
    <linearGradient id="linear-gradient" x1="348.02" y1="-267.36" x2="407.11" y2="-267.36" gradientTransform="translate(485.57 119.7) rotate(-135)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#007991"/>
      <stop offset="1" stop-color="#78ffd6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="378.35" y1="-259.37" y2="-259.37" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-3" x1="46.01" y1="72.97" x2="74.77" y2="72.97" gradientTransform="translate(43.51 119.7) rotate(-45) scale(1 -1)" xlink:href="#linear-gradient"/>
  </defs>
  <g class="cls-4">
    <g id="Layer_2" data-name="Layer 2">
      <g id="OBJECTS">
        <g>
          <path class="cls-1" d="M8.65,62.67h0c11.54,11.54,30.24,11.54,41.78,0h0c11.54-11.54,11.54-30.24,0-41.78L29.54,0,8.65,20.89c-11.54,11.54-11.54,30.24,0,41.78ZM29.54,22.61l9.58,9.58c5.28,5.28,5.28,13.88,0,19.17-5.28,5.28-13.88,5.28-19.17,0s-5.28-13.88,0-19.17l9.58-9.58Z"/>
          <path class="cls-2" d="M18.59,49.99c.27,.27,.56,.53,.85,.79t0,0c-4.73-5.32-4.57-13.49,.53-18.58l9.58-9.58,11.31-11.31L29.54,0l-10.96,10.96c-10.76,10.76-10.76,28.27,0,39.03Z"/>
          <path class="cls-3" d="M40.5,49.99c-.27,.27-.56,.53-.85,.79h0c4.73-5.32,4.57-13.49-.53-18.58l-9.58-9.58-11.31-11.31L29.54,0l10.96,10.96c10.76,10.76,10.76,28.27,0,39.03Z"/>
        </g>
      </g>
    </g>
  </g>
</svg>