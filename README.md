<div align="center">
  <img src="/public/logo.svg" alt="Cashflowee AI Logo" width="200" height="200">
  <h1>Cashflowee AI</h1>
  <p>
    <strong>Ditch the Spreadsheet. Automate Your Finances.</strong>
  </p>
  <p>
    <a href="https://nextjs.org"><img src="https://img.shields.io/badge/Next.js-14-black?style=flat-square&logo=next.js" alt="Next.js"></a>
    <a href="https://tailwindcss.com"><img src="https://img.shields.io/badge/Tailwind-3-38bdf8?style=flat-square&logo=tailwind-css" alt="Tailwind CSS"></a>
    <a href="https://supabase.com"><img src="https://img.shields.io/badge/Supabase-2-3ecf8e?style=flat-square&logo=supabase" alt="Supabase"></a>
    <a href="https://ui.shadcn.com"><img src="https://img.shields.io/badge/shadcn/ui-0.5-black?style=flat-square" alt="shadcn/ui"></a>
  </p>
</div>

## Overview

Cashflowee AI is a personal finance web application that helps users automate their budgeting by uploading bank statements. It features AI-powered transaction categorization, expense tracking, and a beautiful mobile-friendly dashboard.

## ✨ Features

- 🤖 **AI-Powered Transaction Import**: Upload bank statements in CSV/Excel format and let our AI do the heavy lifting
- 🏷️ **Automatic Categorization**: Transactions are intelligently categorized using Google's Gemini AI
- 📊 **Beautiful Visualizations**: See your spending patterns with intuitive charts and breakdowns
- 📱 **Mobile-First Dashboard**: Access your finances from any device with a responsive design
- 🔒 **Secure Authentication**: Your data is protected with Supabase authentication
- 💰 **Free to Start**: Begin managing your finances without any cost

## 🛠️ Tech Stack

- 🔄 **Framework**: [Next.js 14](https://nextjs.org/) with App Router
- 🎨 **UI Components**: [shadcn/ui](https://ui.shadcn.com/) - Beautifully designed components
- 💅 **Styling**: [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- 🔌 **Backend**: [Supabase](https://supabase.com/) - Auth, Storage, and Database
- 📝 **Forms**: [React Hook Form](https://react-hook-form.com/) - Performant form validation
- 📊 **Data Processing**: [PapaParse](https://www.papaparse.com/) (CSV), [XLSX](https://sheetjs.com/) (Excel)
- 🧠 **AI Integration**: [Google Generative AI](https://ai.google.dev/) - Gemini API for transaction categorization
- 🧪 **Testing**: [Jest](https://jestjs.io/) and [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)

## 🚀 Getting Started

### Prerequisites

- Node.js 18.x or later
- npm, yarn, or pnpm
- A Supabase account (free tier works fine)
- Google Gemini API key (optional, for AI categorization)

### Installation

1. **Clone the repository**

```bash
git clone https://github.com/yourusername/v1-cashflowee-ai.git
cd v1-cashflowee-ai
```

2. **Set up environment variables**

Create a `.env.local` file with the following (replace with your credentials):

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Application Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# AI Configuration (Optional)
NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key
NEXT_PUBLIC_USE_AI_CATEGORIZATION=true
```

3. **Install dependencies**

```bash
npm install
# or
yarn install
# or
pnpm install
```

4. **Run the development server**

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. **Open your browser**

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 💻 Usage

1. **Create an Account**: Sign up using your email and password
2. **Access Dashboard**: Navigate to the dashboard after login
3. **Upload Bank Statement**: Use the import tool to upload CSV or Excel files
4. **View Insights**: See categorized expenses, income, and balance in real-time
5. **Explore Categories**: Understand your spending patterns with detailed breakdowns
6. **Filter by Time Period**: View your finances by different time periods (This Month, Last Month, etc.)

<div align="center">
  <img src="/public/images/dashboard-preview.png" alt="Cashflowee AI Dashboard" width="800">
  <p><em>Dashboard Preview</em></p>
</div>

## 💡 Future Enhancements

- 💲 **Budget Setting**: Set and track budgets by category
- 📈 **Financial Goals**: Create and track savings goals
- 📆 **Monthly Reports**: Generate and export financial reports (PDF)
- 👥 **Multi-User Access**: Family/partner collaboration on shared finances
- 📱 **Mobile App**: Native mobile application for iOS and Android
- 💳 **Direct Bank Integration**: Connect directly to bank accounts for automatic updates

## 📚 Documentation

### Key Components

- **Transaction Processing**: The `transaction-utils.ts` file contains the core logic for processing and categorizing transactions
- **AI Integration**: The `gemini-ai.ts` file handles communication with Google's Generative AI for transaction categorization
- **Dashboard**: The main dashboard components are located in `/src/app/dashboard/page.tsx`

### Font System

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a modern font family that provides excellent readability.

## 🛠️ Development

### Running Tests

```bash
npm test
# or
yarn test
```

### Linting

```bash
npm run lint
# or
yarn lint
```

## 🚀 Deployment

The application is designed to be deployed on Vercel, but can be deployed on any platform that supports Next.js applications.

### Deploy on Vercel

The easiest way to deploy is using the [Vercel Platform](https://vercel.com/new) from the creators of Next.js.

1. Push your code to a GitHub repository
2. Import the project into Vercel
3. Set the required environment variables
4. Deploy!

## 👨‍💻 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📜 License

This project is licensed under the MIT License - see the LICENSE file for details.
