# Cashflowee AI – SLC Version Build Prompt

## Objective

Build the SLC (Simple, Lovable, Complete) version of **Cashflowee AI** — a personal finance web app that helps users automate their budgeting by uploading bank statements. Use **Next.js**, **Shadcn UI**, and **Supabase**.

---

## Tech Stack

- **Frontend**: Next.js (App Router)
- **UI Components**: Shadcn UI
- **Styling**: Tailwind CSS
- **Backend**: Supabase (Auth + Storage)
- **Forms**: React Hook Form
- **AI**: Placeholder logic for categorization (mock service)

---

## Pages & Routes

| Route         | Description                                |
| ------------- | ------------------------------------------ |
| `/`           | Landing page                               |
| `/login`      | Authentication page (Supabase email auth)  |
| `/dashboard`  | Main user dashboard                        |

---

## 1. Landing Page (`/`)

### Hero Section
- **Headline**: “Ditch the Spreadsheet. Automate Your Finances.”
- **Subheadline**: “Cashflowee <PERSON> simplifies personal finance with AI-powered bank statement import, automatic categorization, and a beautiful mobile-friendly experience.”
- **CTA Button**: “Start for Free”

### Features Section
- ✅ AI-Powered Transaction Import
- ✅ Automatic Categorization
- ✅ Mobile-First Dashboard
- ✅ Free to Start, Affordable to Upgrade

### Testimonials Section
- Add placeholder testimonials

### Footer
- Links to: About, Privacy, Contact

---

## 2. Authentication (Supabase)
- Email login/signup
- Supabase client configuration
- Redirect to `/dashboard` after login

---

## 3. Dashboard Features (`/dashboard`)

- Upload bank statements via Excel/CSV
- Clean and parse uploaded data
- Apply mock AI logic to categorize transactions
- Display:
  - Expense summary by category
  - Total income
  - Balance overview
- Manual transaction entry fallback

---

## Future Enhancements (Post-SLC)
- Budget setting by category
- Advanced categorization rules
- Monthly financial reports (PDF)
- Subscription tiers
- Family/partner collaboration

---

## Notes

- Use placeholder logic or mock service for categorization to be replaced with real AI pipeline later.
- Focus on mobile-first UI and clean UX from the start.
- Keep file structure modular (e.g., `components/`, `lib/`, `app/`, `utils/`, `hooks/`).

