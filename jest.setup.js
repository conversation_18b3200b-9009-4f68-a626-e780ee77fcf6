// Add any global test setup here
// For example, if you need to mock global objects or set up test environment

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
  }),
}));

// Mock environment variables
process.env = {
  ...process.env,
  NEXT_PUBLIC_GEMINI_API_KEY: 'test-api-key',
  NEXT_PUBLIC_USE_AI_CATEGORIZATION: 'true',
};
