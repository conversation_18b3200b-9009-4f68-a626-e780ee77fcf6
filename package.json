{"name": "v1-cashflowee-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.14", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "@types/papaparse": "^5.3.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "lucide-react": "^0.507.0", "next": "15.3.1", "papaparse": "^5.5.2", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.20"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^29.7.0", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}